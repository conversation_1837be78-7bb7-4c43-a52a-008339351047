# Configurable Prompt System for Telegram Auto-Posting Bot

## Overview

The Telegram Auto-Posting Bot now features a flexible prompt configuration system that allows you to customize the prompts sent to the Gemini 2.5 Flash API for content generation without modifying the source code.

## How It Works

The system supports **three levels of prompt configuration** in order of priority:

1. **Environment Variables** (highest priority)
2. **JSON Configuration File** (`prompts.json`)
3. **Default Fallback Prompts** (built into the code)

## Configuration Methods

### Method 1: JSON Configuration File (Recommended)

The easiest way to customize prompts is by editing the `prompts.json` file in the bot's root directory.

#### Structure of prompts.json:
```json
{
  "daily_news": {
    "description": "Prompt for generating daily news summaries",
    "variables": ["country", "current_date"],
    "prompt": "Your custom prompt here..."
  },
  "cricket_news": {
    "description": "Prompt for generating cricket news updates", 
    "variables": ["current_date"],
    "prompt": "Your custom prompt here..."
  },
  "health_fitness": {
    "description": "Prompt for generating health and fitness content",
    "variables": ["current_date"], 
    "prompt": "Your custom prompt here..."
  },
  "crypto_prices": {
    "description": "Prompt for generating cryptocurrency price updates",
    "variables": ["current_date"],
    "prompt": "Your custom prompt here..."
  }
}
```

#### Available Variables:
- `{country}` - The country for news content (e.g., "India")
- `{current_date}` - Current date in format "Monday, January 15, 2024"

### Method 2: Environment Variables

You can also set prompts using environment variables in the `.env` file:

```env
GEMINI_PROMPT_DAILY_NEWS="Your custom daily news prompt..."
GEMINI_PROMPT_CRICKET_NEWS="Your custom cricket news prompt..."
GEMINI_PROMPT_HEALTH_FITNESS="Your custom health & fitness prompt..."
GEMINI_PROMPT_CRYPTO_PRICES="Your custom crypto prices prompt..."
```

**Note:** Environment variables take precedence over JSON configuration.

## Content Type Requirements

Each content type has specific formatting requirements for Telegram:

### Daily News Summary
- **Character Limit:** 1000-1024 characters total
- **Format:** Start with "*DAILY NEWS SUMMARY*" (bold)
- **Items:** Use italics with underscores: `_News item text._`
- **Emojis:** Include relevant emojis for engagement
- **Spacing:** One blank line between items

### Cricket News
- **Word Limit:** 200 words total
- **Format:** Start with "🏏 *CRICKET NEWS AND UPDATES*"
- **Items:** Use format: `_News item._ 🏆`
- **Structure:** 5 news items with different emojis

### Health & Fitness
- **Character Limit:** 1000-1024 characters total
- **Format:** Start with "💪 *HEALTH & FITNESS*"
- **Content:** Actionable tips and advice
- **Variety:** Rotate topics (nutrition, exercise, mental health, etc.)

### Crypto Prices
- **Character Limit:** 1000-1024 characters total
- **Format:** Start with "💰 *CRYPTO MARKET UPDATE*"
- **Data:** Include prices, percentage changes, trends
- **Structure:** Major coins, gainers/losers, market sentiment

## Testing Your Prompts

To test the prompt configuration system:

```bash
python test_prompts.py
```

This will show:
- Which prompts are loaded from which source
- Sample output from each prompt type
- Configuration status

## Troubleshooting

### Common Issues:

1. **Prompts not loading from JSON:**
   - Check that `prompts.json` exists in the bot directory
   - Verify JSON syntax is valid
   - Check file permissions

2. **Content generation fails:**
   - Ensure prompts include required formatting instructions
   - Check character/word limits are specified
   - Verify variable placeholders are correct

3. **Environment variables not working:**
   - Check `.env` file syntax
   - Restart the bot after changes
   - Verify variable names match exactly

### Log Messages:

The bot logs will show which source is being used:
- `"Successfully loaded and formatted prompt for [type] from JSON configuration"`
- `"Successfully loaded and formatted prompt for [type] from environment variable"`
- `"Successfully loaded and formatted prompt for [type] from default fallback"`

## Best Practices

1. **Test prompts** before deploying to production
2. **Keep character limits** in mind for Telegram captions
3. **Include formatting instructions** in your prompts
4. **Use variables** like `{country}` and `{current_date}` for dynamic content
5. **Backup your custom prompts** before making changes
6. **Monitor bot logs** to ensure prompts are working correctly

## Example Custom Prompt

Here's an example of a custom daily news prompt:

```json
{
  "daily_news": {
    "prompt": "Create a news summary for {country} on {current_date}.\n\nFormat:\n- Start with '*📰 NEWS UPDATE*'\n- Use bullet points with emojis\n- Keep under 1000 characters\n- Focus on top 5 stories\n- Use italics for headlines: _Headline text_\n\nMake it engaging and informative!"
  }
}
```

## Support

If you encounter issues with the prompt configuration system:
1. Check the bot logs for error messages
2. Verify your JSON syntax using a JSON validator
3. Test with the default prompts first
4. Ensure all required variables are included in your custom prompts
