{"daily_news": {"description": "Prompt for generating daily news summaries", "variables": ["country", "current_date"], "prompt": "Generate a daily news summary for {country} for TODAY, {current_date}.\n\nIMPORTANT: You MUST include ONLY THE MOST RECENT NEWS from the past 24 hours. Do NOT include old news from previous days or weeks.\nUse your knowledge cutoff to determine what is current and what is not. If you're unsure if something is current, do not include it.\n\nFormat the summary EXACTLY as follows:\n- Start with \"*DAILY NEWS SUMMARY*\" (bold title)\n- Each news item should be in italics using underscores: _News item text here._\n- Add one blank line between each news item\n- Include relevant emojis for each news item\n- Focus on the most important and impactful news\n- Include a mix of categories: politics, economy, technology, sports, entertainment, etc.\n\nExample format:\n*DAILY NEWS SUMMARY*\n\n_Prime Minister announces new infrastructure development plan worth ₹2 lakh crore._ 🏗️💰\n\n_Indian Space Research Organisation successfully launches communication satellite._ 🚀🛰️\n\n_Stock markets reach new highs as IT sector shows strong growth._ 📈💼\n\nIMPORTANT: The entire summary MUST be between 1000-1024 characters total (including spaces and emojis).\nThis is a strict requirement as it will be used as an image caption with a 1024 character limit.\n\nCRITICAL: All news items MUST be from the LAST 24-48 HOURS ONLY. Do NOT include old news."}, "cricket_news": {"description": "Prompt for generating cricket news updates", "variables": ["current_date"], "prompt": "Give me cricket news of last 24 hours (write them in short, sweet, to the point and catchy language)\n\n🏏 *CRICKET NEWS AND UPDATES*\n\n_News 1 here._ 🏆\n\n_News 2 here._ 🎯\n\n_News 3 here._ 🔥\n\n_News 4 here._ ⚡\n\n_News 5 here._ 🌟\n\nGive output exact same to the format (total output word limit 200). To find news of last 24 hours use web search, google or internet."}, "health_fitness": {"description": "Prompt for generating health and fitness content", "variables": ["current_date"], "prompt": "Generate valuable health, nutrition, and fitness content for {current_date}.\n\nIMPORTANT: Create a well-structured, informative post that provides genuinely useful health and fitness advice.\nFocus on creating content that is readable, valuable, and presents new information each time.\n\nFormat requirements:\n- Start with \"💪 *HEALTH & FITNESS*\" (bold title)\n- Use clear sections with emojis\n- Include practical tips that people can actually implement\n- Make it engaging and motivational\n- Vary the topics each time (nutrition, exercise, mental health, wellness tips, etc.)\n- Use italics for emphasis: _important points_\n- Include relevant emojis throughout\n\nExample topics to rotate:\n- Nutrition tips and healthy recipes\n- Exercise routines and workout advice\n- Mental health and stress management\n- Sleep hygiene and recovery\n- Hydration and supplements\n- Injury prevention\n- Healthy lifestyle habits\n\nIMPORTANT: The entire post MUST be between 1000-1024 characters total (including spaces and emojis).\nThis is a strict requirement as it will be used as an image caption with a 1024 character limit.\n\nFocus on providing ACTIONABLE advice that adds real value to readers' health and fitness journey."}, "crypto_prices": {"description": "Prompt for generating cryptocurrency price updates", "variables": ["current_date"], "prompt": "Generate a daily cryptocurrency price update for {current_date}.\n\nIMPORTANT: You MUST provide the MOST CURRENT cryptocurrency information you have access to.\nIf you're unsure about current prices, focus on the trends and general market conditions instead.\n\nFormat the update EXACTLY as follows:\n- Start with \"💰 *CRYPTO MARKET UPDATE*\" (bold title)\n- Include major cryptocurrencies: Bitcoin (BTC), Ethereum (ETH), Solana (SOL), etc.\n- Show prices in USD format: $XX,XXX.XX\n- Include 24h percentage changes with + or - signs\n- Add relevant emojis for each coin\n- Include market sentiment and trends\n- Add one trending/notable coin\n- Include top gainer and top loser\n\nExample format:\n💰 *CRYPTO MARKET UPDATE*\n\n🟠 _Bitcoin (BTC): $43,250.00 (+2.5%)_\n🔷 _Ethereum (ETH): $2,580.00 (+1.8%)_\n🟣 _Solana (SOL): $98.50 (+4.2%)_\n\n📈 _Top Gainer: AVAX (+12.3%)_\n📉 _Top Loser: DOGE (-3.1%)_\n\n_Market showing bullish momentum with increased institutional adoption._ 🚀\n\nIMPORTANT: The entire update MUST be between 1000-1024 characters total (including spaces and emojis).\nThis is a strict requirement as it will be used as an image caption with a 1024 character limit.\n\nCRITICAL: Focus on providing the MOST CURRENT information available to you."}}