"""
Channel management handlers.
"""
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ConversationHandler

import database as db
from utils.session import get_user_session, clear_session
from utils.keyboards import create_main_menu_keyboard

# Enable logging
logger = logging.getLogger(__name__)

# Conversation states
AWAITING_CHANNEL_FORWARD = 1

async def add_channel(update: Update, context: CallbackContext) -> int:
    """Start the process of adding a channel."""
    # Check if this is a callback query or a direct command
    if update.callback_query:
        query = update.callback_query
        await query.answer()
        await query.edit_message_text(
            "📢 Please forward a message from the channel you want to add.\n\n"
            "⚠️ You must be an admin of the channel to add it.\n\n"
            "To cancel, click the button below or use /cancel.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Cancel", callback_data="cancel_add_channel")]
            ])
        )
    else:
        await update.message.reply_text(
            "📢 Please forward a message from the channel you want to add.\n\n"
            "⚠️ You must be an admin of the channel to add it.\n\n"
            "To cancel, click the button below or use /cancel.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Cancel", callback_data="cancel_add_channel")]
            ])
        )

    return AWAITING_CHANNEL_FORWARD

async def handle_forwarded_message(update: Update, context: CallbackContext) -> int:
    """Handle a forwarded message from a channel."""
    user_id = update.effective_user.id

    # Check if the message is forwarded from a channel
    if not update.message.forward_from_chat or update.message.forward_from_chat.type != 'channel':
        await update.message.reply_text(
            "⚠️ This doesn't appear to be a message forwarded from a channel.\n\n"
            "Please forward a message directly from the channel you want to add, or use /cancel to abort.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("❌ Cancel", callback_data="cancel_add_channel")]
            ])
        )
        return AWAITING_CHANNEL_FORWARD

    # Get channel info
    channel = update.message.forward_from_chat
    channel_id = str(channel.id)
    channel_name = channel.title
    channel_type = channel.type

    # Add channel to database with user_id
    success = db.add_channel(user_id, channel_id, channel_name, channel_type)

    if success:
        await update.message.reply_text(
            f"✅ Channel '{channel_name}' added successfully!\n\n"
            f"You can now create projects for this channel using /addprojects.",
            reply_markup=create_main_menu_keyboard()
        )
    else:
        await update.message.reply_text(
            f"⚠️ Channel '{channel_name}' is already in your list.\n\n"
            f"You can view your channels with /channels.",
            reply_markup=create_main_menu_keyboard()
        )

    return ConversationHandler.END

async def handle_cancel_add_channel(update: Update, context: CallbackContext) -> int:
    """Handle cancellation of adding a channel."""
    query = update.callback_query
    await query.answer()

    await query.edit_message_text(
        "❌ Channel addition cancelled.",
        reply_markup=create_main_menu_keyboard()
    )

    return ConversationHandler.END

async def list_channels(update: Update, context: CallbackContext) -> None:
    """List all channels for the current user."""
    user_id = update.effective_user.id

    # Get channels for this user
    channels = db.get_channels(user_id)

    # Check if this is a callback query or a direct command
    if update.callback_query:
        query = update.callback_query
        await query.answer()

        if not channels:
            await query.edit_message_text(
                "📢 You haven't added any channels yet.\n\n"
                "Use /addchannel to add a channel.",
                reply_markup=create_main_menu_keyboard()
            )
            return

        # Create a message with all channels
        message = "📢 *Your Channels*\n\n"
        for channel_id, channel_info in channels.items():
            channel_name = channel_info.get('name', 'Unknown')
            message += f"• {channel_name}\n"

        message += "\nUse /addchannel to add more channels."

        await query.edit_message_text(
            message,
            parse_mode="Markdown",
            reply_markup=create_main_menu_keyboard()
        )
    else:
        if not channels:
            await update.message.reply_text(
                "📢 You haven't added any channels yet.\n\n"
                "Use /addchannel to add a channel.",
                reply_markup=create_main_menu_keyboard()
            )
            return

        # Create a message with all channels
        message = "📢 *Your Channels*\n\n"
        for channel_id, channel_info in channels.items():
            channel_name = channel_info.get('name', 'Unknown')
            message += f"• {channel_name}\n"

        message += "\nUse /addchannel to add more channels."

        await update.message.reply_text(
            message,
            parse_mode="Markdown",
            reply_markup=create_main_menu_keyboard()
        )
