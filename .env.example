# Telegram Auto-Posting Bot Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# Telegram Bot Token (get from @BotFather on Telegram)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Google Gemini API Key (get from Google AI Studio)
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# OPTIONAL API KEYS
# =============================================================================

# NewsAPI Key (for news content - get from newsapi.org)
NEWS_API_KEY=your_news_api_key_here

# GNews API Key (alternative news source - get from gnews.io)
GNEWS_API_KEY=your_gnews_api_key_here

# =============================================================================
# CONTENT GENERATION PROMPTS (OPTIONAL)
# =============================================================================

# Daily News Summary Prompt
GEMINI_PROMPT_DAILY_NEWS=Generate a concise daily news summary with 5-7 key headlines. Format with bold title and italic news items.

# Cricket News Prompt
GEMINI_PROMPT_CRICKET_NEWS=Create a cricket news update with 5 key cricket stories. Include match results, player updates, and tournament news. Use emojis and keep under 200 words.

# Health & Fitness Prompt
GEMINI_PROMPT_HEALTH_FITNESS=Generate valuable health and fitness content with practical tips, latest research, or workout advice. Make it engaging and informative for general audience.
