"""
Custom button handlers.
"""
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ConversationHandler

import database as db
from utils.session import get_session_data, set_session_data, clear_session
from utils.keyboards import create_custom_buttons_keyboard

# Enable logging
logger = logging.getLogger(__name__)

# Conversation states
ENTERING_BUTTON_TEXT = 5
ENTERING_BUTTON_URL = 6

async def handle_custom_buttons(update: Update, context: CallbackContext) -> None:
    """Handle custom buttons settings."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get button settings
    button_settings = project_info.get('button_settings', {})
    buttons_enabled = button_settings.get('enabled', False)
    buttons = button_settings.get('buttons', [])
    
    # Create message
    message = f"🔘 *Custom Buttons*\n\n"
    
    if buttons:
        message += f"Status: {'Enabled' if buttons_enabled else 'Disabled'}\n\n"
        message += "Current buttons:\n"
        
        for i, button in enumerate(buttons):
            message += f"{i+1}. [{button.get('text', 'Unknown')}]({button.get('url', '#')})\n"
    else:
        message += "No custom buttons added yet.\n\n"
        message += "Add buttons to display below your posts."
    
    # Create keyboard
    keyboard = create_custom_buttons_keyboard(project_id, bool(buttons), buttons_enabled)
    
    # Add buttons to remove existing buttons
    if buttons:
        remove_buttons = []
        for i, button in enumerate(buttons):
            remove_buttons.append(
                InlineKeyboardButton(
                    f"🗑️ Remove '{button.get('text')}'",
                    callback_data=f"remove_button:{project_id}:{i}"
                )
            )
        
        # Add remove buttons to keyboard
        for button in remove_buttons:
            keyboard.inline_keyboard.insert(-1, [button])
    
    await query.edit_message_text(
        message,
        parse_mode="HTML",
        reply_markup=keyboard,
        disable_web_page_preview=True
    )

def handle_add_button(update: Update, context: CallbackContext) -> int:
    """Start the process of adding a custom button."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Store project_id in user session
    user_id = query.from_user.id
    set_session_data(user_id, 'project_id', project_id)
    
    await query.edit_message_text(
        "Please enter the text for your button (max 30 characters):"
    )
    
    return ENTERING_BUTTON_TEXT

def handle_button_text(update: Update, context: CallbackContext) -> int:
    """Handle button text input."""
    user_id = update.effective_user.id
    button_text = update.message.text.strip()
    
    if not button_text:
        update.message.reply_text("Please enter valid button text.")
        return ENTERING_BUTTON_TEXT
    
    if len(button_text) > 30:
        update.message.reply_text("Button text is too long. Please keep it under 30 characters.")
        return ENTERING_BUTTON_TEXT
    
    # Store button text in user session
    set_session_data(user_id, 'button_text', button_text)
    
    update.message.reply_text(
        "Now please enter the URL for your button (must start with http:// or https://):"
    )
    
    return ENTERING_BUTTON_URL

def handle_button_url(update: Update, context: CallbackContext) -> int:
    """Handle button URL input and save the button."""
    user_id = update.effective_user.id
    button_url = update.message.text.strip()
    
    if not button_url.startswith(('http://', 'https://')):
        update.message.reply_text("Please enter a valid URL starting with http:// or https://")
        return ENTERING_BUTTON_URL
    
    # Get session data
    project_id = get_session_data(user_id, 'project_id')
    button_text = get_session_data(user_id, 'button_text')
    
    if not project_id or not button_text:
        update.message.reply_text("Session data missing. Please start over.")
        return ConversationHandler.END
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        update.message.reply_text("Project not found. It may have been deleted.")
        return ConversationHandler.END
    
    # Get button settings
    button_settings = project_info.get('button_settings', {})
    buttons = button_settings.get('buttons', [])
    
    # Add new button
    new_button = {
        "text": button_text,
        "url": button_url
    }
    
    buttons.append(new_button)
    
    # Update button settings
    button_settings['buttons'] = buttons
    if 'enabled' not in button_settings:
        button_settings['enabled'] = True
    
    # Save button settings
    db.update_project_button_settings(project_id, button_settings)
    
    # Create keyboard to go back to button settings
    keyboard = [
        [InlineKeyboardButton("⬅️ Back to Button Settings", callback_data=f"custom_buttons:{project_id}")]
    ]
    
    update.message.reply_text(
        f"✅ Button '{button_text}' added successfully!",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
    
    # Clear session data
    clear_session(user_id)
    
    return ConversationHandler.END

async def handle_toggle_buttons(update: Update, context: CallbackContext) -> None:
    """Handle toggling custom buttons."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get button settings
    button_settings = project_info.get('button_settings', {})
    buttons_enabled = button_settings.get('enabled', False)
    
    # Toggle enabled status
    button_settings['enabled'] = not buttons_enabled
    
    # Save button settings
    db.update_project_button_settings(project_id, button_settings)
    
    # Show updated button settings
    await handle_custom_buttons(update, context)

async def handle_remove_button(update: Update, context: CallbackContext) -> None:
    """Handle removing a custom button."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id and button_index from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    button_index = int(parts[2])
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get button settings
    button_settings = project_info.get('button_settings', {})
    buttons = button_settings.get('buttons', [])
    
    # Check if button index is valid
    if button_index < 0 or button_index >= len(buttons):
        await query.edit_message_text(
            "❌ Invalid button index.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Button Settings", callback_data=f"custom_buttons:{project_id}")]
            ])
        )
        return
    
    # Get button info for confirmation
    button = buttons[button_index]
    button_text = button.get('text', 'Unknown')
    
    # Create confirmation keyboard
    keyboard = [
        [
            InlineKeyboardButton("✅ Yes, Remove", callback_data=f"confirm_remove_button:{project_id}:{button_index}"),
            InlineKeyboardButton("❌ No, Cancel", callback_data=f"custom_buttons:{project_id}")
        ]
    ]
    
    await query.edit_message_text(
        f"⚠️ <b>Remove Button</b>\n\n"
        f"Are you sure you want to remove the button '{button_text}'?\n\n"
        f"This action cannot be undone.",
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def handle_confirm_remove_button(update: Update, context: CallbackContext) -> None:
    """Handle confirming button removal."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id and button_index from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    button_index = int(parts[2])
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get button settings
    button_settings = project_info.get('button_settings', {})
    buttons = button_settings.get('buttons', [])
    
    # Check if button index is valid
    if button_index < 0 or button_index >= len(buttons):
        await query.edit_message_text(
            "❌ Invalid button index.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Button Settings", callback_data=f"custom_buttons:{project_id}")]
            ])
        )
        return
    
    # Get button info
    button = buttons[button_index]
    button_text = button.get('text', 'Unknown')
    
    # Remove button
    buttons.pop(button_index)
    
    # Update button settings
    button_settings['buttons'] = buttons
    
    # Save button settings
    db.update_project_button_settings(project_id, button_settings)
    
    # Show updated button settings
    await handle_custom_buttons(update, context)
