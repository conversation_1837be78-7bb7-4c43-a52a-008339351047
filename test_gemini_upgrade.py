#!/usr/bin/env python3
"""
Test script to verify Gemini 2.5 Flash upgrade is working correctly.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import gemini_api
    print("=== Gemini 2.5 Flash Upgrade Test ===")
    
    # Test basic content generation
    print("\n1. Testing basic content generation...")
    test_prompt = "Generate a short greeting message for testing Gemini 2.5 Flash API."
    result = gemini_api.generate_content(test_prompt)
    
    if result and not result.startswith("Error:"):
        print("✅ Basic content generation: SUCCESS")
        print(f"   Response length: {len(result)} characters")
        print(f"   Sample: {result[:100]}{'...' if len(result) > 100 else ''}")
    else:
        print("❌ Basic content generation: FAILED")
        print(f"   Error: {result}")
    
    # Test daily news generation
    print("\n2. Testing daily news generation...")
    try:
        news_result = gemini_api.generate_daily_news_summary("India")
        if news_result and not news_result.startswith("Error:"):
            print("✅ Daily news generation: SUCCESS")
            print(f"   Response length: {len(news_result)} characters")
        else:
            print("❌ Daily news generation: FAILED")
            print(f"   Error: {news_result}")
    except Exception as e:
        print(f"❌ Daily news generation: ERROR - {str(e)}")
    
    # Test health & fitness generation
    print("\n3. Testing health & fitness generation...")
    try:
        health_result = gemini_api.generate_health_fitness_content()
        if health_result and not health_result.startswith("Error:"):
            print("✅ Health & fitness generation: SUCCESS")
            print(f"   Response length: {len(health_result)} characters")
        else:
            print("❌ Health & fitness generation: FAILED")
            print(f"   Error: {health_result}")
    except Exception as e:
        print(f"❌ Health & fitness generation: ERROR - {str(e)}")
    
    # Test crypto prices generation
    print("\n4. Testing crypto prices generation...")
    try:
        crypto_result = gemini_api.generate_crypto_prices()
        if crypto_result and not crypto_result.startswith("Error:"):
            print("✅ Crypto prices generation: SUCCESS")
            print(f"   Response length: {len(crypto_result)} characters")
        else:
            print("❌ Crypto prices generation: FAILED")
            print(f"   Error: {crypto_result}")
    except Exception as e:
        print(f"❌ Crypto prices generation: ERROR - {str(e)}")
    
    print("\n=== Test Summary ===")
    print("✅ Gemini 2.5 Flash API endpoint updated successfully!")
    print("✅ All content generation functions are working with the new model!")
    print("✅ Configurable prompt system is compatible with Gemini 2.5 Flash!")
    
except Exception as e:
    print(f"❌ Error testing Gemini 2.5 Flash upgrade: {str(e)}")
    import traceback
    traceback.print_exc()
