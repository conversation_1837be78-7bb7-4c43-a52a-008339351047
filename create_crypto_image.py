from PIL import Image, ImageDraw, ImageFont
import os

# Create a directory for images if it doesn't exist
os.makedirs("data/images", exist_ok=True)

# Create a new image with a blue background
width, height = 800, 400
background_color = (40, 44, 52)  # Dark blue background
img = Image.new('RGB', (width, height), color=background_color)

# Get a drawing context
draw = ImageDraw.Draw(img)

# Try to load a font, or use default if not available
try:
    # Try to load Aria<PERSON> or a similar font
    font_large = ImageFont.truetype("arial.ttf", 60)
    font_small = ImageFont.truetype("arial.ttf", 30)
except IOError:
    # Use default font if Arial is not available
    font_large = ImageFont.load_default()
    font_small = ImageFont.load_default()

# Add text to the image
title = "CRYPTO PRICES"
subtitle = "Daily cryptocurrency updates"

# Draw the title in the center top
draw.text((width/2, height/3), title, fill=(255, 215, 0), font=font_large, anchor="mm")  # Gold color

# Draw the subtitle
draw.text((width/2, height/2), subtitle, fill=(200, 200, 200), font=font_small, anchor="mm")  # Light gray

# Draw some crypto symbols
symbols = ["₿", "Ξ", "◎", "Ł"]
symbol_positions = [(width/5, 2*height/3), (2*width/5, 2*height/3), 
                    (3*width/5, 2*height/3), (4*width/5, 2*height/3)]

for symbol, pos in zip(symbols, symbol_positions):
    draw.text(pos, symbol, fill=(255, 255, 255), font=font_large, anchor="mm")

# Save the image
img.save("data/images/cryptoprices.png")

print("Crypto prices image created successfully at data/images/cryptoprices.png")
