"""
Basic command handlers for the bot.
"""
import logging
from telegram import Update, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ConversationHandler

import database as db
import content_poster as poster
import timezone_utils as tz
from utils.session import clear_session
from utils.keyboards import create_main_menu_keyboard

# Enable logging
logger = logging.getLogger(__name__)

async def start(update: Update, context: CallbackContext) -> None:
    """Send a message when the command /start is issued."""
    user = update.effective_user
    try:
        await update.message.reply_text(
            f"👋 *Welcome, {user.first_name}!*\n\n"
            f"📰 I'm your Telegram Auto-Posting Bot that creates daily news summaries for your channels.\n\n"
            f"🔍 *Getting Started:*\n"
            f"1️⃣ First, use /addchannel to add your Telegram channel\n"
            f"2️⃣ Then, use /addprojects to create a new auto-posting project\n"
            f"3️⃣ Configure your project settings and enjoy automated posting!\n\n"
            f"📋 *Available Commands:*\n"
            f"• /addchannel - Add a new channel (forward a message from it)\n"
            f"• /channels - View your added channels\n"
            f"• /addprojects - Create a new daily news summary project\n"
            f"• /projects - Manage your existing projects\n"
            f"• /help - Show this help message\n"
            f"• /cancel - Cancel the current operation\n\n"
            f"🤖 *Bot Version:* 1.0.0",
            parse_mode="Markdown",
            reply_markup=create_main_menu_keyboard()
        )
    except Exception as e:
        logging.error(f"Error in start command: {str(e)}")
        # Fallback to plain text if there's an error with Markdown parsing
        await update.message.reply_text(
            f"👋 Welcome, {user.first_name}!\n\n"
            f"📰 I'm your Telegram Auto-Posting Bot that creates daily news summaries for your channels.\n\n"
            f"🔍 Getting Started:\n"
            f"1. First, use /addchannel to add your Telegram channel\n"
            f"2. Then, use /addprojects to create a new auto-posting project\n"
            f"3. Configure your project settings and enjoy automated posting!\n\n"
            f"📋 Available Commands:\n"
            f"• /addchannel - Add a new channel (forward a message from it)\n"
            f"• /channels - View your added channels\n"
            f"• /addprojects - Create a new daily news summary project\n"
            f"• /projects - Manage your existing projects\n"
            f"• /help - Show this help message\n"
            f"• /cancel - Cancel the current operation\n\n"
            f"🤖 Bot Version: 1.0.0",
            reply_markup=create_main_menu_keyboard()
        )

async def help_command(update: Update, context: CallbackContext) -> None:
    """Send a message when the command /help is issued."""
    await start(update, context)  # Reuse the start command for help

async def cancel(update: Update, context: CallbackContext) -> int:
    """Cancel the current conversation."""
    user = update.effective_user
    user_id = user.id

    # Clear any active session data for this user
    clear_session(user_id)

    # Create a keyboard with main commands
    keyboard = create_main_menu_keyboard()

    await update.message.reply_text(
        "🔄 Operation cancelled. What would you like to do next?",
        reply_markup=keyboard
    )

    return ConversationHandler.END

async def show_server_time(update: Update, context: CallbackContext) -> None:
    """Show the current server time and time in different countries."""
    # Get current time in different countries
    india_time = tz.format_time_for_country("India")
    usa_time = tz.format_time_for_country("USA")
    russia_time = tz.format_time_for_country("Russia")

    await update.message.reply_text(
        f"🕒 *Current Server Time*\n\n"
        f"🇮🇳 *India:* {india_time}\n"
        f"🇺🇸 *USA:* {usa_time}\n"
        f"🇷🇺 *Russia:* {russia_time}\n\n"
        f"The bot uses these times to schedule posts based on your timezone settings.",
        parse_mode="Markdown"
    )

async def check_posting_status(update: Update, context: CallbackContext) -> None:
    """Check the status of posting threads."""
    # Get all projects
    projects = db.get_projects()

    if not projects:
        await update.message.reply_text("No projects found.")
        return

    # Get status of all posting threads
    status_text = "📊 *Posting Status*\n\n"

    for project_id, project_info in projects.items():
        project_name = project_info.get('name', 'Unknown')
        is_active = project_info.get('active', False)

        status = poster.get_posting_thread_status(project_id)

        if status:
            thread_alive = status.get('thread_alive', False)
            content_type = status.get('content_type', 'Unknown')
            post_time = status.get('post_time', 'Unknown')
            current_time = status.get('current_time', 'Unknown')
            timezone_country = status.get('timezone_country', 'Unknown')
            last_posted = status.get('last_posted', 'Never')

            status_emoji = "🟢" if thread_alive and is_active else "🔴"
            status_text += f"{status_emoji} *{project_name}*\n"
            status_text += f"  • Status: {'Active' if is_active else 'Inactive'}\n"
            status_text += f"  • Thread: {'Running' if thread_alive else 'Stopped'}\n"
            status_text += f"  • Content: {content_type}\n"
            status_text += f"  • Post Time: {post_time} ({timezone_country})\n"
            status_text += f"  • Current Time: {current_time} ({timezone_country})\n"
            status_text += f"  • Last Posted: {last_posted}\n\n"
        else:
            status_emoji = "⚪"
            status_text += f"{status_emoji} *{project_name}*\n"
            status_text += f"  • Status: {'Active' if is_active else 'Inactive'}\n"
            status_text += f"  • Thread: Not started\n\n"

    await update.message.reply_text(status_text, parse_mode="Markdown")

async def reset_last_posted_command(update: Update, context: CallbackContext) -> None:
    """Reset the last posted time for a project."""
    # Check if a project ID was provided
    if not context.args or len(context.args) < 1:
        # No project ID provided, show a list of projects
        projects = db.get_projects()

        if not projects:
            await update.message.reply_text("No projects found.")
            return

        # Create a keyboard with all projects
        keyboard = []
        for project_id, project_info in projects.items():
            project_name = project_info.get('name', 'Unknown')
            keyboard.append([
                InlineKeyboardButton(
                    f"🔄 Reset {project_name}",
                    callback_data=f"reset_last_posted:{project_id}"
                )
            ])

        await update.message.reply_text(
            "Select a project to reset its last posted time:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return

    # Project ID was provided
    project_id = context.args[0]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id)

    if not project_info:
        await update.message.reply_text(f"Project with ID {project_id} not found.")
        return

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    if not content_type:
        await update.message.reply_text("Content type not found for this project.")
        return

    # Reset last posted time
    success = db.reset_last_posted_time(project_id, content_type)

    if success:
        await update.message.reply_text(f"✅ Last posted time for project '{project_info.get('name')}' has been reset.")
    else:
        await update.message.reply_text("❌ Failed to reset last posted time.")

async def handle_command_buttons(update: Update, context: CallbackContext) -> None:
    """Handle command buttons."""
    query = update.callback_query
    await query.answer()

    command = query.data.replace("cmd_", "")

    if command == "addchannel":
        from handlers.channel_handlers import add_channel
        await add_channel(update, context)
    elif command == "channels":
        from handlers.channel_handlers import list_channels
        await list_channels(update, context)
    elif command == "addprojects":
        from handlers.project_handlers import add_projects
        await add_projects(update, context)
    elif command == "projects":
        from handlers.project_handlers import list_projects
        await list_projects(update, context)
    elif command == "cancel":
        # Create a new message with the main menu
        await query.edit_message_text(
            "🔄 Operation cancelled. What would you like to do next?",
            reply_markup=create_main_menu_keyboard()
        )
    else:
        await query.edit_message_text(
            f"Unknown command: {command}",
            reply_markup=create_main_menu_keyboard()
        )
