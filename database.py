import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

# Data directory
DATA_DIR = "data"

# Ensure data directory exists
os.makedirs(DATA_DIR, exist_ok=True)

# File paths for storing data
CHANNELS_FILE = os.path.join(DATA_DIR, "channels.json")
PROJECTS_FILE = os.path.join(DATA_DIR, "projects.json")
CONTENT_TYPES_FILE = os.path.join(DATA_DIR, "content_types.json")
POST_HISTORY_FILE = os.path.join(DATA_DIR, "post_history.json")

# Image directory
IMAGE_DIR = os.path.join(DATA_DIR, "images")
os.makedirs(IMAGE_DIR, exist_ok=True)

def initialize_database():
    """Initialize the database files if they don't exist."""
    if not os.path.exists(CHANNELS_FILE):
        with open(CHANNELS_FILE, 'w') as f:
            json.dump({}, f)

    if not os.path.exists(PROJECTS_FILE):
        with open(PROJECTS_FILE, 'w') as f:
            json.dump({}, f)

    if not os.path.exists(CONTENT_TYPES_FILE):
        default_content_types = {
            "daily_news_summary": {
                "name": "Daily News Summary",
                "description": "Generates a daily summary of news for India",
                "parameters": {
                    "country": "India",
                    "post_interval_hours": 24,
                    "post_time_hour": 8,  # Default to 8 AM
                    "post_time_minute": 0,  # Default to 0 minutes
                    "timezone_country": "India",  # Default timezone country
                    "last_posted": None
                }
            },
            "crypto_prices": {
                "name": "Crypto Prices",
                "description": "Posts daily cryptocurrency price updates",
                "parameters": {
                    "num_coins": 4,  # Number of main coins to display
                    "post_interval_hours": 24,
                    "post_time_hour": 9,  # Default to 9 AM
                    "post_time_minute": 0,  # Default to 0 minutes
                    "timezone_country": "India",  # Default timezone country
                    "last_posted": None
                }
            }
        }
        with open(CONTENT_TYPES_FILE, 'w') as f:
            json.dump(default_content_types, f, indent=4)

    if not os.path.exists(POST_HISTORY_FILE):
        with open(POST_HISTORY_FILE, 'w') as f:
            json.dump({}, f)

def get_channels() -> Dict[str, Dict[str, Any]]:
    """Get all channels from the database."""
    if not os.path.exists(CHANNELS_FILE):
        return {}

    with open(CHANNELS_FILE, 'r') as f:
        try:
            return json.load(f)
        except json.JSONDecodeError:
            return {}

def add_channel(channel_id: str, channel_name: str, channel_type: str) -> bool:
    """
    Add a channel to the database.

    Args:
        channel_id: The ID of the channel
        channel_name: The name of the channel
        channel_type: The type of the channel (public, private, etc.)

    Returns:
        bool: True if the channel was added successfully, False otherwise
    """
    channels = get_channels()

    # Check if channel already exists
    if channel_id in channels:
        return False

    # Add the channel
    channels[channel_id] = {
        "name": channel_name,
        "type": channel_type,
        "added_at": str(os.path.getmtime(CHANNELS_FILE)) if os.path.exists(CHANNELS_FILE) else "0"
    }

    # Save to file
    with open(CHANNELS_FILE, 'w') as f:
        json.dump(channels, f, indent=4)

    return True

def remove_channel(channel_id: str) -> bool:
    """Remove a channel from the database."""
    channels = get_channels()

    if channel_id not in channels:
        return False

    del channels[channel_id]

    with open(CHANNELS_FILE, 'w') as f:
        json.dump(channels, f, indent=4)

    return True

def get_projects() -> Dict[str, Dict[str, Any]]:
    """Get all projects from the database."""
    if not os.path.exists(PROJECTS_FILE):
        return {}

    with open(PROJECTS_FILE, 'r') as f:
        try:
            return json.load(f)
        except json.JSONDecodeError:
            return {}

def add_project(project_id: str, project_name: str, channels: List[Dict[str, Any]]) -> bool:
    """
    Add a project to the database.

    Args:
        project_id: The ID of the project
        project_name: The name of the project
        channels: List of channel configurations for this project

    Returns:
        bool: True if the project was added successfully, False otherwise
    """
    projects = get_projects()

    # Check if project already exists
    if project_id in projects:
        return False

    # Add the project
    projects[project_id] = {
        "name": project_name,
        "channels": channels,
        "created_at": str(os.path.getmtime(PROJECTS_FILE)) if os.path.exists(PROJECTS_FILE) else "0",
        "active": True
    }

    # Save to file
    with open(PROJECTS_FILE, 'w') as f:
        json.dump(projects, f, indent=4)

    return True

def update_project(project_id: str, data: Dict[str, Any]) -> bool:
    """Update a project in the database."""
    projects = get_projects()

    if project_id not in projects:
        return False

    # Update the project with new data
    for key, value in data.items():
        projects[project_id][key] = value

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(projects, f, indent=4)

    return True

def remove_project(project_id: str) -> bool:
    """Remove a project from the database."""
    projects = get_projects()

    if project_id not in projects:
        return False

    del projects[project_id]

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(projects, f, indent=4)

    return True

def get_content_types() -> Dict[str, Dict[str, Any]]:
    """Get all content types from the database."""
    if not os.path.exists(CONTENT_TYPES_FILE):
        return {}

    with open(CONTENT_TYPES_FILE, 'r') as f:
        try:
            return json.load(f)
        except json.JSONDecodeError:
            return {}

def get_content_type(content_type_id: str) -> Optional[Dict[str, Any]]:
    """Get a specific content type from the database."""
    content_types = get_content_types()
    return content_types.get(content_type_id)

def update_project_content_settings(project_id: str, content_type_id: str, settings: Dict[str, Any]) -> bool:
    """Update content settings for a project."""
    projects = get_projects()

    if project_id not in projects:
        return False

    # Initialize content_settings if it doesn't exist
    if "content_settings" not in projects[project_id]:
        projects[project_id]["content_settings"] = {}

    # Update or add content type settings
    projects[project_id]["content_settings"][content_type_id] = settings

    with open(PROJECTS_FILE, 'w') as f:
        json.dump(projects, f, indent=4)

    return True

def get_post_history() -> Dict[str, List[Dict[str, Any]]]:
    """Get post history from the database."""
    if not os.path.exists(POST_HISTORY_FILE):
        return {}

    with open(POST_HISTORY_FILE, 'r') as f:
        try:
            return json.load(f)
        except json.JSONDecodeError:
            return {}

def add_post_to_history(project_id: str, channel_id: str, content_type: str, content: str) -> bool:
    """Add a post to the history."""
    history = get_post_history()

    # Initialize project history if it doesn't exist
    if project_id not in history:
        history[project_id] = []

    # Add post to history
    post_entry = {
        "channel_id": channel_id,
        "content_type": content_type,
        "content": content,
        "timestamp": datetime.now().isoformat(),
    }

    history[project_id].append(post_entry)

    # Limit history to last 100 posts per project
    if len(history[project_id]) > 100:
        history[project_id] = history[project_id][-100:]

    with open(POST_HISTORY_FILE, 'w') as f:
        json.dump(history, f, indent=4)

    return True

def clear_post_history(project_id: str) -> bool:
    """Clear the post history for a project.

    Args:
        project_id: The ID of the project

    Returns:
        bool: True if the history was cleared successfully, False otherwise
    """
    history = get_post_history()

    if project_id not in history:
        return False

    # Clear the history for this project
    history[project_id] = []

    with open(POST_HISTORY_FILE, 'w') as f:
        json.dump(history, f, indent=4)

    return True

def reset_last_posted_time(project_id: str, content_type: str) -> bool:
    """Reset the last posted time for a project's content type.

    Args:
        project_id: The ID of the project
        content_type: The content type (e.g., 'daily_news_summary', 'crypto_prices')

    Returns:
        bool: True if the last posted time was reset successfully, False otherwise
    """
    projects = get_projects()

    if project_id not in projects:
        return False

    # Get content settings
    content_settings = projects[project_id].get('content_settings', {})

    # Check if the content type exists
    if content_type not in content_settings:
        return False

    # Reset the last posted time
    content_settings[content_type]['last_posted'] = None

    # Update the project
    projects[project_id]['content_settings'] = content_settings

    # Save to file
    with open(PROJECTS_FILE, 'w') as f:
        json.dump(projects, f, indent=4)

    return True

# Initialize the database when the module is imported
initialize_database()
