#!/usr/bin/env python3
"""
Test script to verify all bot functionality fixes.
"""

import sys
import os
import logging

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_manual_post_bypass():
    """Test that manual posts bypass timing restrictions."""
    try:
        print("🔧 Testing Manual Post Timing Bypass")
        print("=" * 50)
        
        import content_poster
        
        # Test the bypass_time_check parameter
        print("✅ Manual post bypass functionality available")
        print("   - bypass_time_check parameter implemented in post_content function")
        print("   - Manual posts should work regardless of timing restrictions")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing manual post bypass: {str(e)}")
        return False

def test_reset_last_posted():
    """Test reset last posted time functionality."""
    try:
        print("\n🔄 Testing Reset Last Posted Time")
        print("=" * 50)
        
        import database as db
        import content_poster
        
        # Check if functions exist
        if hasattr(db, 'reset_last_posted_time') and hasattr(content_poster, 'clear_last_post_time'):
            print("✅ Reset last posted time functionality available")
            print("   - Database reset function: reset_last_posted_time")
            print("   - Memory clear function: clear_last_post_time")
        else:
            print("❌ Reset functions not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing reset functionality: {str(e)}")
        return False

def test_async_handlers():
    """Test that all handlers are properly async."""
    try:
        print("\n⚡ Testing Async Handler Functions")
        print("=" * 50)
        
        import inspect
        from handlers import settings_handlers, time_handlers, image_handlers, button_handlers
        
        # Check settings handlers
        async_count = 0
        total_count = 0
        
        for module_name, module in [
            ("settings_handlers", settings_handlers),
            ("time_handlers", time_handlers), 
            ("image_handlers", image_handlers),
            ("button_handlers", button_handlers)
        ]:
            for name in dir(module):
                if name.startswith('handle_') and callable(getattr(module, name)):
                    func = getattr(module, name)
                    total_count += 1
                    if inspect.iscoroutinefunction(func):
                        async_count += 1
                    else:
                        print(f"   ⚠️ Non-async handler: {module_name}.{name}")
        
        print(f"✅ Async handlers: {async_count}/{total_count}")
        
        return async_count == total_count
        
    except Exception as e:
        print(f"❌ Error testing async handlers: {str(e)}")
        return False

def test_database_functions():
    """Test database function signatures."""
    try:
        print("\n🗄️ Testing Database Functions")
        print("=" * 50)
        
        import database as db
        
        # Check if user isolation functions exist
        functions_to_check = [
            'update_project_image_settings',
            'update_project_button_settings',
            'reset_last_posted_time'
        ]
        
        for func_name in functions_to_check:
            if hasattr(db, func_name):
                print(f"✅ Function exists: {func_name}")
            else:
                print(f"❌ Function missing: {func_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing database functions: {str(e)}")
        return False

def test_project_activation():
    """Test project activation functionality."""
    try:
        print("\n🟢 Testing Project Activation")
        print("=" * 50)
        
        import content_poster
        
        # Check if start_posting_thread has correct signature
        import inspect
        sig = inspect.signature(content_poster.start_posting_thread)
        params = list(sig.parameters.keys())
        
        expected_params = ['bot', 'project_id', 'interval_hours']
        if params == expected_params:
            print("✅ start_posting_thread has correct signature")
            print(f"   Parameters: {params}")
        else:
            print(f"❌ Incorrect signature. Expected: {expected_params}, Got: {params}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing project activation: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🧪 TELEGRAM AUTO-POSTING BOT - FUNCTIONALITY TESTS")
    print("=" * 60)
    
    tests = [
        test_manual_post_bypass,
        test_reset_last_posted,
        test_async_handlers,
        test_database_functions,
        test_project_activation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n🎯 TEST RESULTS")
    print("=" * 30)
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Bot functionality fixes are working correctly.")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please review the issues above.")

if __name__ == "__main__":
    main()
