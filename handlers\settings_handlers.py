"""
Settings management handlers.
"""
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext

import database as db
import timezone_utils as tz
from utils.session import set_session_data, get_session_data
from utils.keyboards import create_timezone_keyboard

# Enable logging
logger = logging.getLogger(__name__)

async def handle_content_settings(update: Update, context: CallbackContext) -> None:
    """Handle content settings."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    user_id = query.from_user.id

    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    project_name = project_info.get('name', 'Unknown')
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    if not content_type:
        await query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return
    
    # Get content settings
    content_settings = project_info.get('content_settings', {})
    
    # Create settings message
    message = f"📝 *Content Settings: {project_name}*\n\n"
    
    # Add content type
    if content_type == "daily_news_summary":
        message += "📰 *Content Type:* Daily News Summary\n\n"
        
        # Add news settings
        if "daily_news_summary" in content_settings:
            settings = content_settings["daily_news_summary"]
            country = settings.get('country', 'India')
            message += f"🌍 *Country:* {country}\n"
    
    elif content_type == "crypto_prices":
        message += "💰 *Content Type:* Crypto Prices\n\n"
        
        # Add crypto settings
        if "crypto_prices" in content_settings:
            settings = content_settings["crypto_prices"]
            num_coins = settings.get('num_coins', 4)
            message += f"🔢 *Number of Main Coins:* {num_coins}\n"
    
    elif content_type == "health_fitness":
        message += "💪 *Content Type:* Health & Fitness\n\n"
        
        # Health & Fitness doesn't have specific content settings yet
        message += "No specific content settings available for Health & Fitness.\n"
    
    else:
        message += f"📄 *Content Type:* {content_type}\n\n"
        message += "No specific content settings available for this content type.\n"
    
    # Create settings keyboard
    keyboard = []
    
    # Add content-specific settings
    if content_type == "daily_news_summary":
        keyboard.append([
            InlineKeyboardButton("🌍 Change Country", callback_data=f"show_countries:{project_id}")
        ])
    elif content_type == "crypto_prices":
        keyboard.append([
            InlineKeyboardButton("🔢 Set Number of Coins", callback_data=f"configure_content:{project_id}:num_coins")
        ])
    
    # Add back button
    keyboard.append([
        InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")
    ])
    
    await query.edit_message_text(
        message,
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def handle_show_countries(update: Update, context: CallbackContext) -> None:
    """Show country selection for news content."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Store project_id in user session
    user_id = query.from_user.id
    set_session_data(user_id, 'project_id', project_id)
    set_session_data(user_id, 'action', 'set_country')
    
    # Create country selection keyboard
    keyboard = [
        [InlineKeyboardButton("🇮🇳 India", callback_data=f"set_country:{project_id}:India")],
        [InlineKeyboardButton("🇺🇸 USA", callback_data=f"set_country:{project_id}:USA")],
        [InlineKeyboardButton("🇬🇧 UK", callback_data=f"set_country:{project_id}:UK")],
        [InlineKeyboardButton("🇷🇺 Russia", callback_data=f"set_country:{project_id}:Russia")],
        [InlineKeyboardButton("⬅️ Back", callback_data=f"content_settings:{project_id}")]
    ]
    
    await query.edit_message_text(
        "🌍 *Select Country for News*\n\n"
        "Choose the country for which you want to receive news:",
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def handle_set_country(update: Update, context: CallbackContext) -> None:
    """Handle country selection for news content."""
    query = update.callback_query
    await query.answer()

    # Extract project_id and country from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    country = parts[2]
    user_id = update.effective_user.id

    # Get project info
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})

    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})

    # Update country setting
    if "daily_news_summary" in content_settings:
        settings = content_settings["daily_news_summary"]
        settings["country"] = country
        db.update_project_content_settings(user_id, project_id, "daily_news_summary", settings)
    
    # Show updated content settings
    await handle_content_settings(update, context)

async def handle_configure_content(update: Update, context: CallbackContext) -> None:
    """Handle configuring content settings."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id and setting from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    setting = parts[2]
    
    # Get project info
    user_id = query.from_user.id

    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    # Store project_id and setting in user session
    user_id = query.from_user.id
    set_session_data(user_id, 'project_id', project_id)
    set_session_data(user_id, 'setting', setting)
    
    # Handle different settings
    if setting == "num_coins" and content_type == "crypto_prices":
        # Create number selection keyboard
        keyboard = []
        for num in range(3, 8):
            keyboard.append([
                InlineKeyboardButton(f"{num} coins", callback_data=f"set_num_coins:{project_id}:{num}")
            ])
        
        keyboard.append([
            InlineKeyboardButton("⬅️ Back", callback_data=f"content_settings:{project_id}")
        ])
        
        await query.edit_message_text(
            "🔢 *Set Number of Main Coins*\n\n"
            "Select how many main cryptocurrencies to display:",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        await query.edit_message_text(
            "❌ Invalid setting.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back", callback_data=f"content_settings:{project_id}")]
            ])
        )

async def handle_set_num_coins(update: Update, context: CallbackContext) -> None:
    """Handle setting the number of coins for crypto prices."""
    query = update.callback_query
    await query.answer()

    # Extract project_id and num_coins from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    num_coins = int(parts[2])
    user_id = update.effective_user.id

    # Get project info
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})

    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})

    # Update num_coins setting
    if "crypto_prices" in content_settings:
        settings = content_settings["crypto_prices"]
        settings["num_coins"] = num_coins
        db.update_project_content_settings(user_id, project_id, "crypto_prices", settings)
    
    # Show updated content settings
    await handle_content_settings(update, context)

async def handle_set_post_time(update: Update, context: CallbackContext) -> None:
    """Handle setting the posting time."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    user_id = query.from_user.id

    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    if not content_type:
        await query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return
    
    # Get content settings
    content_settings = project_info.get('content_settings', {})
    
    # Get current posting time
    post_time_hour = 8  # Default
    post_time_minute = 0  # Default
    
    if content_type in content_settings:
        settings = content_settings[content_type]
        post_time_hour = settings.get('post_time_hour', 8)
        post_time_minute = settings.get('post_time_minute', 0)
    
    # Create time selection keyboard
    keyboard = []
    
    # Morning times
    morning_row = []
    for hour in range(6, 12):
        time_str = f"{hour:02d}:00"
        morning_row.append(
            InlineKeyboardButton(time_str, callback_data=f"save_post_time:{project_id}:{hour}:0")
        )
    keyboard.append(morning_row)
    
    # Afternoon times
    afternoon_row = []
    for hour in range(12, 18):
        time_str = f"{hour:02d}:00"
        afternoon_row.append(
            InlineKeyboardButton(time_str, callback_data=f"save_post_time:{project_id}:{hour}:0")
        )
    keyboard.append(afternoon_row)
    
    # Evening times
    evening_row = []
    for hour in range(18, 24):
        time_str = f"{hour:02d}:00"
        evening_row.append(
            InlineKeyboardButton(time_str, callback_data=f"save_post_time:{project_id}:{hour}:0")
        )
    keyboard.append(evening_row)
    
    # Custom time option
    keyboard.append([
        InlineKeyboardButton("⌨️ Enter Custom Time", callback_data=f"custom_post_time:{project_id}")
    ])
    
    # Back button
    keyboard.append([
        InlineKeyboardButton("⬅️ Back", callback_data=f"project_settings:{project_id}")
    ])
    
    # Current time display
    current_time = f"{post_time_hour:02d}:{post_time_minute:02d}"
    
    await query.edit_message_text(
        f"⏱️ *Set Posting Time*\n\n"
        f"Current posting time: *{current_time}*\n\n"
        f"Select a new posting time or enter a custom time:",
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def handle_save_post_time(update: Update, context: CallbackContext) -> None:
    """Handle saving the posting time."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id, hour, and minute from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    hour = int(parts[2])
    minute = int(parts[3])
    
    # Get project info for this user
    user_id = query.from_user.id
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    if not content_type:
        await query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return
    
    # Get content settings
    content_settings = project_info.get('content_settings', {})
    
    # Update posting time
    if content_type in content_settings:
        settings = content_settings[content_type]
        settings["post_time_hour"] = hour
        settings["post_time_minute"] = minute
        db.update_project_content_settings(user_id, project_id, content_type, settings)
    
    # Show updated project settings
    from handlers.project_handlers import handle_project_settings
    await handle_project_settings(update, context)

async def handle_set_timezone(update: Update, context: CallbackContext) -> None:
    """Handle setting the timezone."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    user_id = query.from_user.id

    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    if not content_type:
        await query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return
    
    # Get content settings
    content_settings = project_info.get('content_settings', {})
    
    # Get current timezone
    timezone_country = "India"  # Default
    
    if content_type in content_settings:
        settings = content_settings[content_type]
        timezone_country = settings.get('timezone_country', 'India')
    
    # Create timezone selection keyboard
    keyboard = []
    
    # Add all countries
    countries = tz.get_all_countries()
    for country in countries:
        # Add checkmark to current timezone
        country_text = f"✅ {country}" if country == timezone_country else country
        keyboard.append([
            InlineKeyboardButton(country_text, callback_data=f"save_timezone:{project_id}:{country}")
        ])
    
    # Back button
    keyboard.append([
        InlineKeyboardButton("⬅️ Back", callback_data=f"project_settings:{project_id}")
    ])
    
    # Current time in selected timezone
    current_time = tz.format_time_for_country(timezone_country)
    
    await query.edit_message_text(
        f"🌐 *Set Timezone*\n\n"
        f"Current timezone: *{timezone_country}*\n"
        f"Current time: *{current_time}*\n\n"
        f"Select a new timezone:",
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def handle_save_timezone(update: Update, context: CallbackContext) -> None:
    """Handle saving the timezone."""
    query = update.callback_query
    await query.answer()

    # Extract project_id and country from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    country = parts[2]
    user_id = update.effective_user.id

    # Get project info
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})

    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    if not content_type:
        await query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})

    # Update timezone
    if content_type in content_settings:
        settings = content_settings[content_type]
        settings["timezone_country"] = country
        db.update_project_content_settings(user_id, project_id, content_type, settings)

    # Show updated project settings
    from handlers.project_handlers import handle_project_settings
    await handle_project_settings(update, context)

async def handle_show_intervals(update: Update, context: CallbackContext) -> None:
    """Show interval selection for content posting."""
    query = update.callback_query
    await query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Store project_id in user session
    user_id = query.from_user.id
    set_session_data(user_id, 'project_id', project_id)
    set_session_data(user_id, 'action', 'set_interval')

    # Create interval selection keyboard
    keyboard = [
        [InlineKeyboardButton("⏰ Every 12 hours", callback_data=f"set_interval:{project_id}:12")],
        [InlineKeyboardButton("📅 Daily (24 hours)", callback_data=f"set_interval:{project_id}:24")],
        [InlineKeyboardButton("📆 Every 2 days", callback_data=f"set_interval:{project_id}:48")],
        [InlineKeyboardButton("📊 Every 3 days", callback_data=f"set_interval:{project_id}:72")],
        [InlineKeyboardButton("📋 Weekly", callback_data=f"set_interval:{project_id}:168")],
        [InlineKeyboardButton("⬅️ Back", callback_data=f"content_settings:{project_id}")]
    ]

    await query.edit_message_text(
        "⏰ *Select Posting Interval*\n\n"
        "Choose how often you want to post content:",
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

async def handle_set_interval(update: Update, context: CallbackContext) -> None:
    """Handle interval selection for content posting."""
    query = update.callback_query
    await query.answer()

    # Extract project_id and interval from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    interval_hours = int(parts[2])
    user_id = update.effective_user.id

    # Get project info
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})

    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    if not content_type:
        await query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Content Settings", callback_data=f"content_settings:{project_id}")]
            ])
        )
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})

    # Update interval setting
    if content_type in content_settings:
        settings = content_settings[content_type]
        settings["post_interval_hours"] = interval_hours
        db.update_project_content_settings(user_id, project_id, content_type, settings)

    # Show updated content settings
    await handle_content_settings(update, context)

async def handle_save_settings(update: Update, context: CallbackContext) -> None:
    """Handle saving general settings."""
    query = update.callback_query
    await query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    user_id = query.from_user.id

    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})

    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return

    # Show updated content settings
    await handle_content_settings(update, context)
