#!/usr/bin/env python3
"""
<PERSON>ript to fix double await statements.
"""

import re
import os

def fix_double_await(file_path):
    """Fix double await statements in a file."""
    print(f"Fixing double await in {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix double await statements
    content = re.sub(r'await await ', 'await ', content)

    # Also fix any remaining double awaits that might have different spacing
    content = re.sub(r'await\s+await\s+', 'await ', content)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed double await in {file_path}")

def main():
    """Main function to fix double await issues."""
    handler_files = [
        'handlers/settings_handlers.py',
        'handlers/image_handlers.py', 
        'handlers/button_handlers.py',
        'handlers/time_handlers.py'
    ]
    
    for file_path in handler_files:
        if os.path.exists(file_path):
            fix_double_await(file_path)
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    main()
