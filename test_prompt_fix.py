#!/usr/bin/env python3
"""
Test script to verify the AI prompt editing fixes.
"""
import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'handlers'))

import prompt_config
from prompt_handlers import escape_markdown

def test_prompt_fixes():
    """Test all the prompt-related fixes."""
    print("🧪 Testing AI Prompt Editing Fixes")
    print("=" * 50)
    
    # Test 1: Prompt configuration with missing variables
    print("\n1. 🔧 TESTING PROMPT CONFIGURATION:")
    
    try:
        # This should fail gracefully and return escaped prompt
        prompt_without_data = prompt_config.get_prompt("cricket_news", current_date="TODAY")
        print(f"   ✅ Cricket prompt without news_data: {len(prompt_without_data)} chars")
        print(f"   Contains escaped braces: {'\\{' in prompt_without_data}")
        print(f"   First 100 chars: {prompt_without_data[:100]}...")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    try:
        # This should work correctly
        prompt_with_data = prompt_config.get_prompt("cricket_news", current_date="TODAY", news_data="Sample data")
        print(f"   ✅ Cricket prompt with news_data: {len(prompt_with_data)} chars")
        print(f"   Contains unescaped braces: {'{' in prompt_with_data}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Markdown escaping
    print("\n2. 🔒 TESTING MARKDOWN ESCAPING:")
    
    test_strings = [
        "Normal text",
        "Text with *asterisks* and _underscores_",
        "Code with `backticks`",
        "Links with [brackets] and (parentheses)",
        "Variables with {curly} braces",
        "Mixed: *bold* _italic_ `code` [link] {var}"
    ]
    
    for test_str in test_strings:
        escaped = escape_markdown(test_str)
        print(f"   Original: {test_str}")
        print(f"   Escaped:  {escaped}")
        print()
    
    # Test 3: Simulate the actual message creation
    print("3. 📝 TESTING MESSAGE CREATION:")
    
    # Get cricket prompt (this might fail formatting)
    cricket_prompt = prompt_config.get_prompt("cricket_news", current_date="TODAY")
    
    # Simulate the handler logic
    display_prompt = cricket_prompt[:200] + "..." if len(cricket_prompt) > 200 else cricket_prompt
    escaped_prompt = escape_markdown(display_prompt)
    
    message = f"🤖 *AI Prompt Settings*\n\n"
    message += f"Content Type: *Cricket News*\n\n"
    message += "📋 *Using Default Prompt*\n\n"
    message += f"`{escaped_prompt}`\n\n"
    message += "You are using the default system prompt."
    
    print(f"   Message length: {len(message)} chars")
    print(f"   Contains unescaped braces: {'{' in message and '\\{' not in message}")
    print(f"   Contains unescaped asterisks: {'*' in message and '\\*' not in message}")
    print(f"   Contains unescaped underscores: {'_' in message and '\\_' not in message}")
    
    print("\n   First 300 chars of message:")
    print(f"   {repr(message[:300])}")
    
    # Test 4: Check for problematic characters at specific positions
    print("\n4. 🎯 TESTING SPECIFIC POSITIONS:")
    
    if len(message) > 209:
        char_209 = message[209]
        print(f"   Character at position 209: {repr(char_209)}")
        
        # Check surrounding area
        start = max(0, 200)
        end = min(len(message), 220)
        surrounding = message[start:end]
        print(f"   Surrounding text (200-220): {repr(surrounding)}")
    else:
        print(f"   Message is only {len(message)} chars (shorter than 209)")
    
    print("\n" + "=" * 50)
    print("🧪 Test completed!")

if __name__ == "__main__":
    test_prompt_fixes()
