import logging
import telegram
from datetime import datetime, timedelta
import time
import threading
import os
import importlib.util
from dotenv import load_dotenv

import database as db
import gemini_api as gemini
import crypto_api as crypto
import timezone_utils as tz
import image_search as img_search

# Check if <PERSON><PERSON> is installed
PILLOW_AVAILABLE = importlib.util.find_spec("PIL") is not None
if not PILLOW_AVAILABLE:
    logging.warning("Pillow library not found. Image creation functionality will be limited.")

# Load environment variables
load_dotenv()

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO
)
logger = logging.getLogger(__name__)

# Global variables
posting_threads = {}  # Store active posting threads
last_post_times = {}  # Store last post times for each project

def send_message_to_channel(bot, channel_id, message):
    """
    Send a message to a Telegram channel.

    Args:
        bot: The Telegram bot instance
        channel_id: The ID of the channel
        message: The message to send

    Returns:
        tuple: (success, error_message)
    """
    try:
        # First check if the bot is a member of the channel
        try:
            bot_member = bot.get_chat_member(chat_id=channel_id, user_id=bot.id)
            if bot_member.status not in ['administrator', 'creator']:
                error_msg = f"Bot is not an admin in this channel (status: {bot_member.status})"
                logger.error(f"Error sending message to channel {channel_id}: {error_msg}")
                return False, error_msg

            # Check posting permission
            if hasattr(bot_member, 'can_post_messages') and not bot_member.can_post_messages:
                error_msg = "Bot doesn't have permission to post messages"
                logger.error(f"Error sending message to channel {channel_id}: {error_msg}")
                return False, error_msg

        except Exception as e:
            error_msg = f"Failed to check bot permissions: {str(e)}"
            logger.error(f"Error sending message to channel {channel_id}: {error_msg}")
            return False, error_msg

        # Get project info to check for custom image
        projects = db.get_projects()
        project_id = None

        # Find the project that contains this channel
        for pid, project in projects.items():
            for channel_config in project.get('channels', []):
                if channel_config.get('channel_id') == channel_id:
                    project_id = pid
                    break
            if project_id:
                break

        # Check if a custom image is set for this project
        content_type = None
        if project_id:
            project_info = projects.get(project_id, {})
            for channel_config in project_info.get('channels', []):
                content_type = channel_config.get('content_type')
                if content_type:
                    break

        # Set default image based on content type
        if content_type == "crypto_prices":
            default_image_path = os.path.join(db.IMAGE_DIR, 'cryptoprices.png')  # Crypto image
        elif content_type == "health_fitness":
            default_image_path = os.path.join(db.IMAGE_DIR, 'healthfitness.png')  # Health & Fitness image
        else:
            default_image_path = os.path.join(db.IMAGE_DIR, 'dailynewssummary.png')  # News image

        image_path = default_image_path

        # Ensure default image exists
        if not os.path.exists(default_image_path):
            logger.warning(f"Default image not found at {default_image_path}. Creating a placeholder image.")
            try:
                # Create a simple placeholder image if Pillow is available
                if PILLOW_AVAILABLE:
                    from PIL import Image, ImageDraw, ImageFont
                    # Create different placeholder images based on content type
                    if content_type == "crypto_prices":
                        bg_color = (75, 139, 59)  # Green
                        text = "Crypto Prices"
                    elif content_type == "health_fitness":
                        bg_color = (142, 68, 173)  # Purple
                        text = "Health & Fitness"
                    else:  # daily_news_summary or default
                        bg_color = (73, 109, 137)  # Blue
                        text = "Daily News Summary"

                    img = Image.new('RGB', (800, 400), color=bg_color)
                    d = ImageDraw.Draw(img)
                    d.text((400, 200), text, fill=(255, 255, 255), anchor="mm")
                    os.makedirs(os.path.dirname(default_image_path), exist_ok=True)
                    img.save(default_image_path)
                    image_path = default_image_path
                else:
                    logger.warning("Cannot create placeholder image: Pillow library not installed.")
                    # Will fall back to text-only message
            except Exception as e:
                logger.error(f"Failed to create placeholder image: {e}")
                # Will fall back to text-only message

        if project_id:
            # Get content settings
            content_settings = projects[project_id].get('content_settings', {})

            # Get the appropriate settings based on content type
            if content_type == "daily_news_summary":
                settings = content_settings.get('daily_news_summary', {})
            elif content_type == "crypto_prices":
                settings = content_settings.get('crypto_prices', {})
            elif content_type == "health_fitness":
                settings = content_settings.get('health_fitness', {})
            else:
                settings = {}

            # Get image settings
            image_settings = settings.get('image_settings', {
                'mode': 'default',  # 'default', 'custom', or 'internet'
                'custom_image_path': None
            })

            # Determine which image to use based on mode
            mode = image_settings.get('mode', 'default')

            if mode == 'custom':
                # Use custom image if available
                custom_image_path = image_settings.get('custom_image_path')
                if custom_image_path and os.path.exists(custom_image_path):
                    image_path = custom_image_path
            elif mode == 'internet':
                # Get a random internet image for this content type
                try:
                    # Always force download a new image each time and clear the cache
                    # Delete any existing images in the directory first
                    if content_type == "daily_news_summary":
                        image_dir = img_search.NEWS_IMAGES_DIR
                    elif content_type == "crypto_prices":
                        image_dir = img_search.CRYPTO_IMAGES_DIR
                    elif content_type == "health_fitness":
                        image_dir = img_search.HEALTH_FITNESS_IMAGES_DIR
                    else:
                        image_dir = img_search.INTERNET_IMAGES_DIR

                    # Clear all non-placeholder images to ensure we get a fresh one
                    try:
                        for f in os.listdir(image_dir):
                            file_path = os.path.join(image_dir, f)
                            if os.path.isfile(file_path) and f.lower().endswith(('.png', '.jpg', '.jpeg')) and not f.startswith('placeholder_'):
                                try:
                                    os.remove(file_path)
                                    logger.info(f"Removed existing image to force fresh download: {file_path}")
                                except Exception as e:
                                    logger.error(f"Error removing image {file_path}: {e}")
                    except Exception as e:
                        logger.error(f"Error clearing image cache: {e}")

                    # Now get a new image
                    internet_image = img_search.get_random_image_for_content_type(content_type, force_download=True)
                    if internet_image and os.path.exists(internet_image):
                        logger.info(f"Using internet image: {internet_image}")
                        image_path = internet_image
                    else:
                        logger.warning(f"Failed to get internet image, falling back to default")
                except Exception as e:
                    logger.error(f"Error getting internet image: {e}, falling back to default")
            # else: use default image (already set)

        # Check if image exists
        if not os.path.exists(image_path):
            logger.warning(f"Image not found at {image_path}")
            # If image not found, send text message as fallback
            bot.send_message(
                chat_id=channel_id,
                text=formatted_caption,
                parse_mode="Markdown"
            )
            logger.info(f"Image not found, sent text message to channel {channel_id}")
            return True, ""

        # Ensure caption is within 1024 character limit
        caption = message
        if len(caption) > 1024:
            caption = caption[:1021] + "..."

        # Keep the asterisks for formatting as they appear in plain text
        # Don't remove any formatting as we want to keep the asterisks

        # Convert the text to proper Telegram formatting
        # Replace the asterisk formatting with proper Markdown
        formatted_caption = caption

        # Get project info to check for custom buttons
        projects = db.get_projects()
        project_id = None

        # Find the project that contains this channel
        for pid, project in projects.items():
            for channel_config in project.get('channels', []):
                if channel_config.get('channel_id') == channel_id:
                    project_id = pid
                    break
            if project_id:
                break

        # Check if custom buttons are enabled
        reply_markup = None
        if project_id:
            content_settings = projects[project_id].get('content_settings', {})

            # Get content type from the first channel
            content_type = None
            for channel_config in projects[project_id].get('channels', []):
                content_type = channel_config.get('content_type')
                if content_type:
                    break

            # Get the appropriate settings based on content type
            if content_type == "daily_news_summary":
                settings = content_settings.get('daily_news_summary', {})
            elif content_type == "crypto_prices":
                settings = content_settings.get('crypto_prices', {})
            elif content_type == "health_fitness":
                settings = content_settings.get('health_fitness', {})
            else:
                settings = {}

            custom_buttons = settings.get('custom_buttons', {'enabled': False, 'buttons': []})

            if custom_buttons.get('enabled', False) and custom_buttons.get('buttons', []):
                # Create inline keyboard with custom buttons
                keyboard = []
                for button in custom_buttons.get('buttons', []):
                    keyboard.append([telegram.InlineKeyboardButton(
                        text=button.get('text', 'Button'),
                        url=button.get('url', 'https://telegram.org')
                    )])
                reply_markup = telegram.InlineKeyboardMarkup(keyboard)

        # Send photo with caption using Telegram's Markdown formatting
        try:
            with open(image_path, 'rb') as photo:
                bot.send_photo(
                    chat_id=channel_id,
                    photo=photo,
                    caption=formatted_caption,
                    parse_mode="Markdown",  # Use Markdown to properly format the text
                    reply_markup=reply_markup
                )
        except telegram.error.BadRequest as e:
            logger.error(f"Bad request error when posting to channel {channel_id}: {e}")
            if "chat not found" in str(e).lower():
                return False, f"Channel not found. The bot may have been removed from the channel or the channel ID is invalid."
            elif "not enough rights" in str(e).lower():
                return False, f"The bot doesn't have permission to post in this channel. Please make sure the bot is an admin with posting rights."
            else:
                return False, f"Error posting to channel: {e}"
        except telegram.error.Unauthorized as e:
            logger.error(f"Unauthorized error when posting to channel {channel_id}: {e}")
            return False, "The bot token is invalid or the bot has been blocked by the user."
        except telegram.error.NetworkError as e:
            logger.error(f"Network error when posting to channel {channel_id}: {e}")
            return False, f"Network error when posting: {e}. Please try again later."
        except Exception as e:
            logger.error(f"Unexpected error when posting to channel {channel_id}: {e}")
            return False, f"Unexpected error: {e}"

        logger.info(f"Successfully sent message with image to channel {channel_id}")
        return True, ""
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error sending message to channel {channel_id}: {error_msg}")
        return False, error_msg

def generate_daily_news_summary_post(country="India"):
    """
    Generate a daily news summary post.

    Args:
        country: The country to generate news for

    Returns:
        str: The generated post
    """
    return gemini.generate_daily_news_summary(country)

def generate_crypto_prices_post():
    """
    Generate a crypto prices post.

    Returns:
        str: The generated post
    """
    # Use the CoinGecko API directly
    try:
        return crypto.generate_crypto_post()
    except Exception as e:
        logger.error(f"Error using crypto API: {e}")
        return f"Error generating crypto prices: {str(e)}"

def generate_health_fitness_post():
    """
    Generate a health and fitness post.

    Returns:
        str: The generated post
    """
    return gemini.generate_health_fitness_content()

def post_content(bot, project_id, test_mode=False, bypass_time_check=False):
    """
    Post content for a project.

    Args:
        bot: The Telegram bot instance
        project_id: The ID of the project
        test_mode: Whether this is a test post (ignores posting interval)
        bypass_time_check: Whether to bypass the time check (for manual posts)

    Returns:
        tuple: (success, message)
    """
    if bypass_time_check:
        logger.info(f"Bypassing time check for project {project_id} - manual post or force post")
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id)

    if not project_info:
        return False, "Project not found"

    if not project_info.get("active", True) and not test_mode:
        return False, "Project is inactive"

    # Get content settings
    content_settings = project_info.get("content_settings", {})

    # Get the content type from the first channel in the project
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Handle different content types
    if content_type == "daily_news_summary":
        if "daily_news_summary" not in content_settings:
            return False, "No daily news summary settings found for this project"

        news_settings = content_settings["daily_news_summary"]
        country = news_settings.get("country", "India")
        post_interval_hours = news_settings.get("post_interval_hours", 24)

        # Check if enough time has passed since the last post (unless it's a test)
        if not test_mode and project_id in last_post_times:
            last_post_time = datetime.fromisoformat(last_post_times[project_id])
            time_since_last_post = datetime.now() - last_post_time

            if time_since_last_post < timedelta(hours=post_interval_hours):
                hours_to_wait = post_interval_hours - (time_since_last_post.total_seconds() / 3600)
                return False, f"Not enough time has passed since the last post. Wait {hours_to_wait:.1f} more hours."

        # Generate daily news summary content
        content = generate_daily_news_summary_post(country)
        content_type_name = "daily_news_summary"

    elif content_type == "crypto_prices":
        if "crypto_prices" not in content_settings:
            return False, "No crypto prices settings found for this project"

        crypto_settings = content_settings["crypto_prices"]
        post_interval_hours = crypto_settings.get("post_interval_hours", 24)

        # Check if enough time has passed since the last post (unless it's a test)
        if not test_mode and project_id in last_post_times:
            last_post_time = datetime.fromisoformat(last_post_times[project_id])
            time_since_last_post = datetime.now() - last_post_time

            if time_since_last_post < timedelta(hours=post_interval_hours):
                hours_to_wait = post_interval_hours - (time_since_last_post.total_seconds() / 3600)
                return False, f"Not enough time has passed since the last post. Wait {hours_to_wait:.1f} more hours."

        # Generate crypto prices content
        content = generate_crypto_prices_post()
        content_type_name = "crypto_prices"

    elif content_type == "health_fitness":
        if "health_fitness" not in content_settings:
            return False, "No health & fitness settings found for this project"

        health_settings = content_settings["health_fitness"]
        post_interval_hours = health_settings.get("post_interval_hours", 24)

        # Check if enough time has passed since the last post (unless it's a test)
        if not test_mode and project_id in last_post_times:
            last_post_time = datetime.fromisoformat(last_post_times[project_id])
            time_since_last_post = datetime.now() - last_post_time

            if time_since_last_post < timedelta(hours=post_interval_hours):
                hours_to_wait = post_interval_hours - (time_since_last_post.total_seconds() / 3600)
                return False, f"Not enough time has passed since the last post. Wait {hours_to_wait:.1f} more hours."

        # Generate health & fitness content
        content = generate_health_fitness_post()
        content_type_name = "health_fitness"

    else:
        return False, f"Unsupported content type: {content_type}"

    if content.startswith("Error:"):
        return False, f"Failed to generate content: {content}"

    # Post to all channels in the project
    success_count = 0
    error_messages = []

    for channel_config in project_info.get("channels", []):
        channel_id = channel_config.get("channel_id")
        channel_name = channel_config.get("channel_name", "Unknown")

        if not channel_id:
            continue

        # Send message to channel
        success, error_msg = send_message_to_channel(bot, channel_id, content)

        if success:
            success_count += 1

            # Add to post history
            db.add_post_to_history(
                project_id,
                channel_id,
                content_type_name,
                content
            )
        else:
            error_messages.append(f"Channel '{channel_name}': {error_msg}")

    # Get timezone country from settings
    if content_type == "daily_news_summary" and "daily_news_summary" in content_settings:
        timezone_country = content_settings["daily_news_summary"].get("timezone_country", "India")
        settings = content_settings["daily_news_summary"]
        content_type_name = "daily_news_summary"
    elif content_type == "crypto_prices" and "crypto_prices" in content_settings:
        timezone_country = content_settings["crypto_prices"].get("timezone_country", "India")
        settings = content_settings["crypto_prices"]
        content_type_name = "crypto_prices"
    elif content_type == "health_fitness" and "health_fitness" in content_settings:
        timezone_country = content_settings["health_fitness"].get("timezone_country", "India")
        settings = content_settings["health_fitness"]
        content_type_name = "health_fitness"
    else:
        timezone_country = "India"

    # Get current time in the selected timezone
    country_time = tz.get_current_time_for_country(timezone_country)

    # Update last post time in memory
    last_post_times[project_id] = country_time.isoformat()

    # Update last posted time in database if successful
    if success_count > 0 and not test_mode:
        settings["last_posted"] = country_time.strftime("%Y-%m-%d %H:%M")
        db.update_project_content_settings(project_id, content_type_name, settings)
        logger.info(f"Updated last posted time for project {project_id} to {country_time.strftime('%Y-%m-%d %H:%M')} ({timezone_country} time)")

    if success_count > 0:
        return True, f"Successfully posted to {success_count} channel(s)"
    else:
        error_details = "\n".join(error_messages)
        return False, f"Failed to post to any channels:\n{error_details}\n\nPlease make sure the bot is an admin in the channel with 'Post Messages' permission."

def start_posting_thread(bot, project_id, interval_hours):
    """
    Start a thread to post content at regular intervals.

    Args:
        bot: The Telegram bot instance
        project_id: The ID of the project
        interval_hours: The interval between posts in hours
    """
    # Stop existing thread if it exists
    stop_posting_thread(project_id)

    # Create a new thread
    thread = threading.Thread(
        target=posting_thread_worker,
        args=(bot, project_id, interval_hours),
        daemon=True
    )

    posting_threads[project_id] = {
        "thread": thread,
        "stop_flag": threading.Event()
    }

    thread.start()

def posting_thread_worker(bot, project_id, interval_hours):
    """
    Worker function for the posting thread.

    Args:
        bot: The Telegram bot instance
        project_id: The ID of the project
        interval_hours: The interval between posts in hours (used when starting the thread)
    """
    stop_flag = posting_threads[project_id]["stop_flag"]

    # Get project info to check posting time
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    content_settings = project_info.get('content_settings', {})

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Get the appropriate settings based on content type
    if content_type == "daily_news_summary":
        settings = content_settings.get('daily_news_summary', {})
    elif content_type == "crypto_prices":
        settings = content_settings.get('crypto_prices', {})
    elif content_type == "health_fitness":
        settings = content_settings.get('health_fitness', {})
    else:
        settings = {}

    # Get posting time settings
    post_time_hour = int(settings.get('post_time_hour', 21))  # Default to 9 PM
    post_time_minute = int(settings.get('post_time_minute', 0))  # Default to 0 minutes
    timezone_country = settings.get('timezone_country', 'India')  # Default to India

    # Log the settings for debugging
    logger.info(f"Project {project_id} settings: post_time_hour={post_time_hour}, post_time_minute={post_time_minute}, timezone_country={timezone_country}")

    # Get current time in the selected timezone
    country_time = tz.get_current_time_for_country(timezone_country)

    # Log the scheduled posting time for debugging
    time_str = f"{post_time_hour:02d}:{post_time_minute:02d}"
    logger.info(f"Project {project_id} scheduled to post at {time_str} ({timezone_country} time)")
    logger.info(f"Current time in {timezone_country}: {country_time.strftime('%Y-%m-%d %H:%M:%S')}")

    while not stop_flag.is_set():
        # Check if it's time to post based on the timezone country
        country_time = tz.get_current_time_for_country(timezone_country)
        current_hour = country_time.hour
        current_minute = country_time.minute

        # Log current time in the timezone every hour (for debugging)
        if datetime.now().minute == 0:
            logger.info(f"Current time in {timezone_country}: {country_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Refresh project settings to get the latest values
        projects = db.get_projects()
        project_info = projects.get(project_id, {})
        content_settings = project_info.get('content_settings', {})

        # Get content type from the first channel
        content_type = None
        if project_info.get("channels"):
            content_type = project_info["channels"][0].get("content_type")

        # Get the appropriate settings based on content type
        if content_type == "daily_news_summary":
            settings = content_settings.get('daily_news_summary', {})
        elif content_type == "crypto_prices":
            settings = content_settings.get('crypto_prices', {})
        elif content_type == "health_fitness":
            settings = content_settings.get('health_fitness', {})
        else:
            settings = {}

        # Get posting time settings
        post_time_hour = int(settings.get('post_time_hour', 21))  # Default to 9 PM
        post_time_minute = int(settings.get('post_time_minute', 0))  # Default to 0 minutes
        timezone_country = settings.get('timezone_country', 'India')  # Default to India

        # Get current time in the selected timezone
        country_time = tz.get_current_time_for_country(timezone_country)
        current_hour = country_time.hour
        current_minute = country_time.minute

        # Check if it's the right time to post (hour and minute)
        logger.info(f"Checking posting time for project {project_id}: Current time {current_hour}:{current_minute}, Scheduled time {post_time_hour}:{post_time_minute}")

        # Check every minute instead of just at the exact minute to avoid missing the posting window
        # This will check if we're within 1 minute of the scheduled time
        is_posting_hour = current_hour == post_time_hour
        is_posting_minute = current_minute == post_time_minute

        logger.info(f"Time check: is_posting_hour={is_posting_hour}, is_posting_minute={is_posting_minute}")

        if is_posting_hour and is_posting_minute:
            logger.info(f"It's posting time for project {project_id}! Current time: {country_time.strftime('%Y-%m-%d %H:%M:%S')} ({timezone_country} time)")
            # Only post if we haven't posted in the last day
            last_posted = settings.get('last_posted')
            should_post = True

            if last_posted:
                try:
                    # Check if enough time has passed since last post using timezone-aware comparison
                    should_post = tz.is_posting_time(
                        post_time_hour,
                        timezone_country,
                        last_posted,
                        min_hours_between_posts=20,
                        scheduled_minute=post_time_minute,
                        bypass_time_check=False  # Don't bypass for scheduled posts
                    )

                    if not should_post:
                        logger.info(f"Skipping post for project {project_id} - posted too recently ({last_posted})")
                except Exception as e:
                    # If time checking fails, proceed with posting
                    logger.warning(f"Error checking posting time: {str(e)} for project {project_id}")
                    pass

            if should_post:
                # Post content
                success, message = post_content(bot, project_id)

                if success:
                    logger.info(f"Posted content for project {project_id}: {message}")
                else:
                    logger.warning(f"Failed to post content for project {project_id}: {message}")

        # Wait for a while before checking again (check every minute)
        for _ in range(60):  # 1 minute in seconds
            if stop_flag.is_set():
                break
            time.sleep(1)

def stop_posting_thread(project_id):
    """
    Stop the posting thread for a project.

    Args:
        project_id: The ID of the project
    """
    if project_id in posting_threads:
        posting_threads[project_id]["stop_flag"].set()
        posting_threads[project_id]["thread"].join(timeout=1)
        del posting_threads[project_id]
        logger.info(f"Stopped posting thread for project {project_id}")

def restart_posting_thread(bot, project_id):
    """
    Restart the posting thread for a project.

    Args:
        bot: The Telegram bot instance
        project_id: The ID of the project
    """
    # Stop the thread if it's running
    if project_id in posting_threads:
        stop_posting_thread(project_id)

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        logger.warning(f"Cannot restart posting thread for project {project_id} - project not found")
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})
    content_type = None

    # Get content type from the first channel
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Get interval hours based on content type
    interval_hours = 24  # Default

    if content_type == "daily_news_summary" and "daily_news_summary" in content_settings:
        news_settings = content_settings["daily_news_summary"]
        interval_hours = news_settings.get("post_interval_hours", 24)
        post_time_hour = news_settings.get("post_time_hour", 9)
        post_time_minute = news_settings.get("post_time_minute", 0)
        logger.info(f"Restarting posting thread for project {project_id} (Daily News Summary) with schedule {post_time_hour:02d}:{post_time_minute:02d}")

    elif content_type == "crypto_prices" and "crypto_prices" in content_settings:
        crypto_settings = content_settings["crypto_prices"]
        interval_hours = crypto_settings.get("post_interval_hours", 24)
        post_time_hour = crypto_settings.get("post_time_hour", 9)
        post_time_minute = crypto_settings.get("post_time_minute", 0)
        logger.info(f"Restarting posting thread for project {project_id} (Crypto Prices) with schedule {post_time_hour:02d}:{post_time_minute:02d}")

    # Start the thread
    start_posting_thread(bot, project_id, interval_hours)

def start_all_active_projects(bot):
    """
    Start posting threads for all active projects.

    Args:
        bot: The Telegram bot instance
    """
    projects = db.get_all_projects_for_posting()

    for project_id, project_info in projects.items():
        if project_info.get("active", True):
            content_settings = project_info.get("content_settings", {})
            content_type = None

            # Get content type from the first channel
            if project_info.get("channels"):
                content_type = project_info["channels"][0].get("content_type")

            # Handle different content types
            if content_type == "daily_news_summary" and "daily_news_summary" in content_settings:
                news_settings = content_settings["daily_news_summary"]
                interval_hours = news_settings.get("post_interval_hours", 24)

                start_posting_thread(bot, project_id, interval_hours)
                logger.info(f"Started posting thread for project {project_id} (Daily News Summary)")

            elif content_type == "crypto_prices" and "crypto_prices" in content_settings:
                crypto_settings = content_settings["crypto_prices"]
                interval_hours = crypto_settings.get("post_interval_hours", 24)

                start_posting_thread(bot, project_id, interval_hours)
                logger.info(f"Started posting thread for project {project_id} (Crypto Prices)")

def stop_all_posting_threads():
    """Stop all posting threads."""
    for project_id in list(posting_threads.keys()):
        stop_posting_thread(project_id)

    logger.info("Stopped all posting threads")

def manual_post(bot, project_id):
    """
    Manually trigger a post for a project, regardless of the scheduled time.

    Args:
        bot: The Telegram bot instance
        project_id: The ID of the project

    Returns:
        tuple: (success, message)
    """
    logger.info(f"Manually triggering post for project {project_id}")

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        return False, f"Project {project_id} not found"

    # Post content with bypass_time_check=True
    success, message = post_content(bot, project_id, bypass_time_check=True)

    if success:
        logger.info(f"Manually posted content for project {project_id}: {message}")
    else:
        logger.error(f"Failed to manually post content for project {project_id}: {message}")

    return success, message

def get_posting_thread_status(project_id=None):
    """
    Get the status of posting threads.

    Args:
        project_id: Optional project ID to get status for a specific project

    Returns:
        dict: Status information for posting threads
    """
    if project_id:
        # Get status for a specific project
        if project_id in posting_threads:
            thread_info = posting_threads[project_id]
            is_alive = thread_info["thread"].is_alive()
            is_stopped = thread_info["stop_flag"].is_set()

            # Get project info
            projects = db.get_projects()
            project_info = projects.get(project_id, {})
            content_settings = project_info.get('content_settings', {})
            content_type = None

            # Get content type from the first channel
            if project_info.get("channels"):
                content_type = project_info["channels"][0].get("content_type")

            # Get settings based on content type
            if content_type == "daily_news_summary" and "daily_news_summary" in content_settings:
                settings = content_settings["daily_news_summary"]
            elif content_type == "crypto_prices" and "crypto_prices" in content_settings:
                settings = content_settings["crypto_prices"]
            else:
                settings = {}

            post_time_hour = settings.get('post_time_hour', 21)  # Default to 9 PM
            post_time_minute = settings.get('post_time_minute', 0)  # Default to 0 minutes
            timezone_country = settings.get('timezone_country', 'India')  # Default to India
            last_posted = settings.get('last_posted')

            # Get current time in the selected timezone
            country_time = tz.get_current_time_for_country(timezone_country)
            current_hour = country_time.hour
            current_minute = country_time.minute

            return {
                "project_id": project_id,
                "thread_alive": is_alive,
                "thread_stopped": is_stopped,
                "content_type": content_type,
                "post_time": f"{post_time_hour:02d}:{post_time_minute:02d}",
                "current_time": f"{current_hour:02d}:{current_minute:02d}",
                "timezone_country": timezone_country,
                "last_posted": last_posted
            }
        else:
            return {"project_id": project_id, "error": "No posting thread found for this project"}
    else:
        # Get status for all projects
        status = {}
        for pid in posting_threads:
            status[pid] = get_posting_thread_status(pid)
        return status
