#!/usr/bin/env python3
"""
Script to fix user isolation issues in handler files.
"""

import re
import os

def fix_user_isolation(file_path):
    """Fix user isolation issues in a handler file."""
    print(f"Fixing user isolation in {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to find functions that need user_id but don't have it
    # Look for functions that call db.get_projects() without user_id
    lines = content.split('\n')
    new_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Check if this line calls db.get_projects() without user_id
        if 'projects = db.get_projects()' in line and 'user_id' not in line:
            # Look backwards to find the function start and check if user_id is already extracted
            func_start = i
            while func_start > 0 and not lines[func_start].strip().startswith('async def handle_'):
                func_start -= 1
            
            # Check if user_id is already extracted in this function
            has_user_id = False
            for j in range(func_start, i):
                if 'user_id = ' in lines[j]:
                    has_user_id = True
                    break
            
            if not has_user_id:
                # Add user_id extraction before the db.get_projects() call
                indent = len(line) - len(line.lstrip())
                user_id_line = ' ' * indent + 'user_id = query.from_user.id'
                new_lines.append(user_id_line)
                new_lines.append('')  # Empty line for readability
            
            # Replace the db.get_projects() call to include user_id
            new_line = line.replace('projects = db.get_projects()', 'projects = db.get_projects(user_id)')
            new_lines.append(new_line)
        else:
            new_lines.append(line)
        
        i += 1
    
    # Join the lines back
    content = '\n'.join(new_lines)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed user isolation in {file_path}")

def main():
    """Main function to fix user isolation issues."""
    handler_files = [
        'handlers/settings_handlers.py',
        'handlers/image_handlers.py', 
        'handlers/button_handlers.py'
    ]
    
    for file_path in handler_files:
        if os.path.exists(file_path):
            fix_user_isolation(file_path)
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    main()
