# 🚀 Production Deployment Guide

This guide will help you deploy the Telegram Auto-Posting Bot in a production environment.

## 📋 Pre-Deployment Checklist

### ✅ Required API Keys
- [ ] Telegram Bot Token (from @BotFather)
- [ ] Google Gemini API Key (from Google AI Studio)
- [ ] NewsAPI Key (optional, from newsapi.org)
- [ ] GNews API Key (optional, from gnews.io)

### ✅ System Requirements
- [ ] Python 3.8 or higher
- [ ] At least 1GB RAM
- [ ] 5GB disk space (for images and logs)
- [ ] Stable internet connection
- [ ] Linux/Windows/macOS server

## 🔧 Installation Steps

### 1. Clone and Setup
```bash
# Clone the repository
git clone <your-repository-url>
cd Auto-posting-bot

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration file
nano .env  # or your preferred editor
```

Fill in your API keys and configuration:
```env
TELEGRAM_BOT_TOKEN=your_actual_bot_token
GEMINI_API_KEY=your_actual_gemini_key
NEWS_API_KEY=your_actual_news_key
GNEWS_API_KEY=your_actual_gnews_key
```

### 3. Test Installation
```bash
# Test with production manager
python start_bot.py
```

## 🐧 Linux Production Deployment

### Using systemd (Recommended)

1. **Create service file**:
```bash
sudo nano /etc/systemd/system/telegram-autopost-bot.service
```

2. **Service configuration**:
```ini
[Unit]
Description=Telegram Auto-Posting Bot
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/Auto-posting-bot
Environment=PATH=/path/to/Auto-posting-bot/venv/bin
ExecStart=/path/to/Auto-posting-bot/venv/bin/python start_bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

3. **Enable and start service**:
```bash
sudo systemctl daemon-reload
sudo systemctl enable telegram-autopost-bot
sudo systemctl start telegram-autopost-bot
```

4. **Check status**:
```bash
sudo systemctl status telegram-autopost-bot
sudo journalctl -u telegram-autopost-bot -f
```

### Using PM2 (Alternative)

1. **Install PM2**:
```bash
npm install -g pm2
```

2. **Create ecosystem file**:
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'telegram-autopost-bot',
    script: 'start_bot.py',
    interpreter: 'python3',
    cwd: '/path/to/Auto-posting-bot',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

3. **Start with PM2**:
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## 🪟 Windows Production Deployment

### Using NSSM (Non-Sucking Service Manager)

1. **Download NSSM** from https://nssm.cc/download

2. **Install service**:
```cmd
nssm install TelegramAutoPostBot
```

3. **Configure service**:
- Path: `C:\path\to\python.exe`
- Startup directory: `C:\path\to\Auto-posting-bot`
- Arguments: `start_bot.py`

4. **Start service**:
```cmd
nssm start TelegramAutoPostBot
```

### Using Task Scheduler (Alternative)

1. Open Task Scheduler
2. Create Basic Task
3. Set trigger to "When the computer starts"
4. Set action to start `start_bot.py`
5. Configure to restart on failure

## 🐳 Docker Deployment

### 1. Create Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["python", "start_bot.py"]
```

### 2. Create docker-compose.yml
```yaml
version: '3.8'
services:
  telegram-bot:
    build: .
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
```

### 3. Deploy
```bash
docker-compose up -d
```

## 📊 Monitoring and Maintenance

### Log Monitoring
```bash
# View logs
tail -f bot.log
tail -f bot_startup.log

# Systemd logs
sudo journalctl -u telegram-autopost-bot -f
```

### Health Checks
- Monitor bot response time
- Check API rate limits
- Monitor disk space for images
- Check memory usage

### Backup Strategy
```bash
# Backup data directory
tar -czf backup-$(date +%Y%m%d).tar.gz data/

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /backups/telegram-bot-$DATE.tar.gz data/
find /backups -name "telegram-bot-*.tar.gz" -mtime +7 -delete
```

## 🔒 Security Considerations

### Environment Security
- Keep `.env` file secure (never commit to git)
- Use proper file permissions (600 for .env)
- Regular security updates

### API Security
- Monitor API usage and quotas
- Implement rate limiting if needed
- Use HTTPS for all external requests

### Bot Security
- Regular bot token rotation
- Monitor unauthorized access attempts
- Implement user access controls

## 🚨 Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check internet connection
   - Verify bot token
   - Check Telegram API status

2. **Content generation failing**
   - Verify Gemini API key
   - Check API quotas
   - Review error logs

3. **Image upload issues**
   - Check disk space
   - Verify image permissions
   - Check file size limits

4. **Memory issues**
   - Monitor memory usage
   - Restart service if needed
   - Consider increasing server resources

### Log Analysis
```bash
# Check for errors
grep -i error bot.log

# Check API failures
grep -i "api" bot.log | grep -i "error"

# Monitor posting activity
grep -i "posted" bot.log
```

## 📞 Support

For production support:
1. Check logs for specific error messages
2. Verify all API keys and quotas
3. Test network connectivity
4. Review system resources

---

**🎯 Your bot is now ready for production use!**
