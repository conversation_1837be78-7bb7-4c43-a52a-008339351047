"""
Cricket News API module for generating cricket news using Gemini API.
"""
import logging
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Enable logging
logger = logging.getLogger(__name__)

# Import Gemini API
try:
    import gemini_api
except ImportError:
    logger.error("Failed to import gemini_api module")
    gemini_api = None

# Import prompt configuration
try:
    import prompt_config
except ImportError:
    logger.error("Failed to import prompt_config module")
    prompt_config = None

def generate_cricket_news_with_gemini() -> str:
    """
    Generate cricket news content using Gemini API.

    Returns:
        str: Formatted cricket news content ready for Telegram posting
    """
    if not gemini_api:
        logger.error("Gemini API module not available")
        return get_fallback_cricket_content()

    try:
        # Get configurable prompt for cricket news
        if prompt_config:
            prompt = prompt_config.get_cricket_news_prompt()
            logger.info("Using configurable prompt for cricket news generation")
        else:
            # Fallback to hardcoded prompt if prompt_config is not available
            prompt = """Give me cricket news of last 24 hours (write them in short, sweet, to the point and catchy language)

🏏 *CRICKET NEWS AND UPDATES*

_News 1 here._ 🏆

_News 2 here._ 🎯

_News 3 here._ 🔥

_News 4 here._ ⚡

_News 5 here._ 🌟

Give output exact same to the format (total output word limit 200)"""
            logger.warning("prompt_config module not available, using fallback prompt")

        logger.info("Generating cricket news content using Gemini API")

        # Generate content using Gemini API
        content = gemini_api.generate_content(prompt)

        if content and not content.startswith("Error:"):
            logger.info("Cricket news content generated successfully using Gemini API")
            return content
        else:
            logger.warning(f"Gemini API returned error: {content}")
            return get_fallback_cricket_content()

    except Exception as e:
        logger.error(f"Error generating cricket news with Gemini API: {str(e)}")
        return get_fallback_cricket_content()

def get_fallback_cricket_content() -> str:
    """
    Get fallback cricket news content when Gemini API is not available.

    Returns:
        str: Fallback cricket news content in the required format
    """
    current_date = datetime.now().strftime("%B %d, %Y")

    fallback_content = f"""🏏 *CRICKET NEWS AND UPDATES*

_Latest cricket updates for {current_date}_

🔸 _Cricket action continues worldwide with exciting matches and tournaments_
🔸 _Players showcase incredible skills in ongoing series and leagues_
🔸 _Fans eagerly await upcoming fixtures and championship battles_
🔸 _Stay tuned for more thrilling cricket moments and match highlights_

🏆 _Follow your favorite teams and players for the latest cricket excitement!_"""

    logger.info("Using fallback cricket content")
    return fallback_content

def generate_cricket_news_content() -> str:
    """
    Generate cricket news content for posting.

    This is the main function called by the content posting system.
    It uses Gemini API to generate fresh cricket news content.

    Returns:
        str: Formatted cricket news content ready for posting
    """
    try:
        logger.info("Generating cricket news content")

        # Generate content using Gemini API
        content = generate_cricket_news_with_gemini()

        # Ensure content doesn't exceed Telegram's caption limit (1024 characters)
        if len(content) > 1024:
            content = content[:1021] + "..."
            logger.warning("Cricket news content truncated to fit Telegram limit")

        logger.info("Cricket news content generated successfully")
        return content

    except Exception as e:
        logger.error(f"Error generating cricket news content: {str(e)}")
        return get_fallback_cricket_content()

# Legacy function maintained for backward compatibility
def fetch_cricket_news(limit: int = 8):
    """
    Legacy function maintained for backward compatibility.
    Now redirects to Gemini-based content generation.

    Args:
        limit: Number of articles to fetch (ignored in new implementation)

    Returns:
        str: Generated cricket news content
    """
    logger.info("Legacy fetch_cricket_news called, redirecting to Gemini-based generation")
    return generate_cricket_news_content()

# Legacy function maintained for backward compatibility
def format_cricket_news_for_telegram(articles):
    """
    Legacy function maintained for backward compatibility.
    Now redirects to Gemini-based content generation.

    Args:
        articles: List of cricket news articles (ignored in new implementation)

    Returns:
        str: Generated cricket news content
    """
    logger.info("Legacy format_cricket_news_for_telegram called, redirecting to Gemini-based generation")
    return generate_cricket_news_content()
