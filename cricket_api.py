"""
Cricket News API module for fetching and formatting cricket news.
"""
import logging
import requests
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Enable logging
logger = logging.getLogger(__name__)

# Cricket news sources and APIs
NEWSAPI_KEY = os.getenv("NEWSAPI_API_KEY")
GNEWS_API_KEY = os.getenv("GNEWS_API_KEY")

def fetch_cricket_news_from_newsapi(limit: int = 10) -> List[Dict[str, Any]]:
    """
    Fetch cricket news from NewsAPI.
    
    Args:
        limit: Number of articles to fetch
        
    Returns:
        List of cricket news articles
    """
    if not NEWSAPI_KEY:
        logger.warning("NewsAPI key not found")
        return []
    
    try:
        # Search for cricket news
        url = "https://newsapi.org/v2/everything"
        params = {
            "q": "cricket OR IPL OR ICC OR cricket match OR cricket tournament",
            "language": "en",
            "sortBy": "publishedAt",
            "pageSize": limit,
            "apiKey": NEWSAPI_KEY
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        articles = data.get("articles", [])
        
        # Filter and clean articles
        cricket_articles = []
        for article in articles:
            if article.get("title") and article.get("description"):
                # Check if it's actually cricket-related
                title_lower = article["title"].lower()
                desc_lower = article["description"].lower()
                
                cricket_keywords = ["cricket", "ipl", "icc", "test match", "odi", "t20", "wicket", "batting", "bowling"]
                if any(keyword in title_lower or keyword in desc_lower for keyword in cricket_keywords):
                    cricket_articles.append({
                        "title": article["title"],
                        "description": article["description"],
                        "url": article.get("url", ""),
                        "published_at": article.get("publishedAt", ""),
                        "source": article.get("source", {}).get("name", "Unknown")
                    })
        
        logger.info(f"Fetched {len(cricket_articles)} cricket articles from NewsAPI")
        return cricket_articles[:limit]
        
    except Exception as e:
        logger.error(f"Error fetching cricket news from NewsAPI: {str(e)}")
        return []

def fetch_cricket_news_from_gnews(limit: int = 10) -> List[Dict[str, Any]]:
    """
    Fetch cricket news from GNews API.
    
    Args:
        limit: Number of articles to fetch
        
    Returns:
        List of cricket news articles
    """
    if not GNEWS_API_KEY:
        logger.warning("GNews API key not found")
        return []
    
    try:
        # Search for cricket news
        url = "https://gnews.io/api/v4/search"
        params = {
            "q": "cricket",
            "lang": "en",
            "country": "in",  # Focus on Indian cricket news
            "max": limit,
            "apikey": GNEWS_API_KEY
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        articles = data.get("articles", [])
        
        # Format articles
        cricket_articles = []
        for article in articles:
            if article.get("title") and article.get("description"):
                cricket_articles.append({
                    "title": article["title"],
                    "description": article["description"],
                    "url": article.get("url", ""),
                    "published_at": article.get("publishedAt", ""),
                    "source": article.get("source", {}).get("name", "Unknown")
                })
        
        logger.info(f"Fetched {len(cricket_articles)} cricket articles from GNews")
        return cricket_articles
        
    except Exception as e:
        logger.error(f"Error fetching cricket news from GNews: {str(e)}")
        return []

def get_fallback_cricket_news() -> List[Dict[str, Any]]:
    """
    Get fallback cricket news when APIs are not available.
    
    Returns:
        List of fallback cricket news items
    """
    fallback_news = [
        {
            "title": "🏏 Cricket Update: Stay tuned for the latest cricket news!",
            "description": "Follow your favorite teams and players for exciting cricket updates.",
            "url": "",
            "published_at": datetime.now().isoformat(),
            "source": "Cricket Bot"
        },
        {
            "title": "🏆 Tournament Alert: Major cricket tournaments happening worldwide",
            "description": "Don't miss out on the thrilling matches and spectacular performances.",
            "url": "",
            "published_at": datetime.now().isoformat(),
            "source": "Cricket Bot"
        },
        {
            "title": "⚡ Match Highlights: Catch up on the latest match results",
            "description": "Amazing catches, powerful sixes, and incredible bowling performances.",
            "url": "",
            "published_at": datetime.now().isoformat(),
            "source": "Cricket Bot"
        }
    ]
    
    return fallback_news

def fetch_cricket_news(limit: int = 8) -> List[Dict[str, Any]]:
    """
    Fetch cricket news from multiple sources.
    
    Args:
        limit: Number of articles to fetch
        
    Returns:
        List of cricket news articles
    """
    all_articles = []
    
    # Try GNews first (better for cricket news)
    gnews_articles = fetch_cricket_news_from_gnews(limit // 2)
    all_articles.extend(gnews_articles)
    
    # Try NewsAPI if we need more articles
    if len(all_articles) < limit:
        remaining = limit - len(all_articles)
        newsapi_articles = fetch_cricket_news_from_newsapi(remaining)
        all_articles.extend(newsapi_articles)
    
    # Use fallback if no articles found
    if not all_articles:
        logger.warning("No cricket news found from APIs, using fallback")
        all_articles = get_fallback_cricket_news()
    
    return all_articles[:limit]

def format_cricket_news_for_telegram(articles: List[Dict[str, Any]]) -> str:
    """
    Format cricket news articles for Telegram posting.
    
    Args:
        articles: List of cricket news articles
        
    Returns:
        Formatted cricket news content
    """
    if not articles:
        return "🏏 *CRICKET NEWS AND UPDATES*\n\n_No cricket news available at the moment. Stay tuned for exciting updates!_"
    
    # Start with title
    content = "🏏 *CRICKET NEWS AND UPDATES*\n\n"
    
    # Add articles
    for i, article in enumerate(articles[:6], 1):  # Limit to 6 articles for readability
        title = article.get("title", "Cricket Update")
        description = article.get("description", "")
        
        # Clean and shorten title if needed
        if len(title) > 80:
            title = title[:77] + "..."
        
        # Clean and shorten description if needed
        if len(description) > 120:
            description = description[:117] + "..."
        
        # Format the news item
        content += f"🔸 _{title}_\n"
        if description and description != title:
            content += f"   {description}\n"
        content += "\n"
    
    # Add footer
    content += "🏆 _Stay updated with the latest cricket action!_"
    
    # Ensure content doesn't exceed Telegram's caption limit
    if len(content) > 1000:
        # Truncate and add ellipsis
        content = content[:997] + "..."
    
    return content

def generate_cricket_news_content() -> str:
    """
    Generate cricket news content for posting.
    
    Returns:
        Formatted cricket news content ready for posting
    """
    try:
        logger.info("Generating cricket news content")
        
        # Fetch cricket news
        articles = fetch_cricket_news(limit=8)
        
        # Format for Telegram
        formatted_content = format_cricket_news_for_telegram(articles)
        
        logger.info("Cricket news content generated successfully")
        return formatted_content
        
    except Exception as e:
        logger.error(f"Error generating cricket news content: {str(e)}")
        return "🏏 *CRICKET NEWS AND UPDATES*\n\n_Unable to fetch cricket news at the moment. Please try again later._"
