"""
Cricket News API module for generating cricket news using NewsAPI and Gemini API.
"""
import logging
from datetime import datetime
import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Enable logging
logger = logging.getLogger(__name__)

# NewsAPI configuration
NEWSAPI_KEY = "********************************"
NEWSAPI_URL = "https://newsapi.org/v2/top-headlines"

# Import Gemini API
try:
    import gemini_api
except ImportError:
    logger.error("Failed to import gemini_api module")
    gemini_api = None

# Import prompt configuration
try:
    import prompt_config
except ImportError:
    logger.error("Failed to import prompt_config module")
    prompt_config = None

def fetch_latest_sports_news() -> list:
    """
    Fetch latest sports news from NewsAPI for India.
    Tries multiple approaches to get relevant sports/cricket content.

    Returns:
        list: List of news articles from NewsAPI
    """
    try:
        logger.info("Fetching latest sports news from NewsAPI")

        # Try multiple search strategies
        search_strategies = [
            # Strategy 1: Sports category for India
            {
                'country': 'in',
                'category': 'sports',
                'apiKey': NEWSAPI_KEY,
                'pageSize': 20
            },
            # Strategy 2: Search for cricket specifically
            {
                'q': 'cricket',
                'language': 'en',
                'sortBy': 'publishedAt',
                'apiKey': NEWSAPI_KEY,
                'pageSize': 20
            },
            # Strategy 3: General news from India (might contain sports)
            {
                'country': 'in',
                'apiKey': NEWSAPI_KEY,
                'pageSize': 30
            }
        ]

        for i, params in enumerate(search_strategies):
            try:
                logger.info(f"Trying search strategy {i+1}: {params}")

                # Use different endpoint for search queries
                if 'q' in params:
                    url = "https://newsapi.org/v2/everything"
                else:
                    url = NEWSAPI_URL

                response = requests.get(url, params=params, timeout=10)
                response.raise_for_status()

                data = response.json()

                if data.get('status') == 'ok' and data.get('articles'):
                    articles = data['articles']
                    logger.info(f"Strategy {i+1} successful: fetched {len(articles)} articles from NewsAPI")
                    return articles
                else:
                    logger.warning(f"Strategy {i+1} returned no articles: {data.get('message', 'No articles found')}")

            except Exception as e:
                logger.warning(f"Strategy {i+1} failed: {str(e)}")
                continue

        logger.warning("All NewsAPI strategies failed, no articles fetched")
        return []

    except Exception as e:
        logger.error(f"Unexpected error fetching sports news: {str(e)}")
        return []

def generate_cricket_news_with_gemini(custom_prompt=None) -> str:
    """
    Generate cricket news content using NewsAPI data and Gemini API.

    Args:
        custom_prompt (str): Custom prompt for this project

    Returns:
        str: Formatted cricket news content ready for Telegram posting
    """
    if not gemini_api:
        logger.error("Gemini API module not available")
        return get_fallback_cricket_content()

    try:
        # Fetch latest sports news from NewsAPI
        news_articles = fetch_latest_sports_news()

        if not news_articles:
            logger.warning("No news articles fetched, using fallback content")
            return get_fallback_cricket_content()

        # Prepare news data for Gemini
        news_data = []
        for article in news_articles:
            if article.get('title') and article.get('description'):
                news_item = {
                    'title': article['title'],
                    'description': article['description'],
                    'source': article.get('source', {}).get('name', 'Unknown'),
                    'publishedAt': article.get('publishedAt', '')
                }
                news_data.append(news_item)

        # Create enhanced prompt with news data
        news_text = "\n".join([
            f"Title: {item['title']}\nDescription: {item['description']}\nSource: {item['source']}\n"
            for item in news_data[:15]  # Limit to avoid token limits
        ])

        # Get configurable prompt for cricket news with news data
        if prompt_config:
            enhanced_prompt = prompt_config.get_cricket_news_prompt(news_data=news_text, custom_prompt=custom_prompt)
            logger.info("Using configurable prompt for cricket news generation with NewsAPI data")
        else:
            # Fallback to hardcoded prompt if prompt_config is not available
            enhanced_prompt = f"""Based on the following latest sports news from India, filter and select the 5 most relevant CRICKET-specific news items and convert them into catchy, engaging format:

{news_text}

INSTRUCTIONS:
1. Select ONLY cricket-related news from the above articles
2. If there are fewer than 5 cricket news items, create engaging cricket content based on current cricket context
3. Format the output EXACTLY as follows:

🏏 *CRICKET NEWS AND UPDATES*

_News 1 here._ 🏆

_News 2 here._ 🎯

_News 3 here._ 🔥

_News 4 here._ ⚡

_News 5 here._ 🌟

REQUIREMENTS:
- Keep each news item under 25 words to fit within 1000 characters total
- Each news item should be concise and engaging
- Use the exact emoji sequence (🏆, 🎯, 🔥, ⚡, 🌟)
- Make the content catchy and exciting for cricket fans
- Focus on the most recent and relevant cricket updates
- MUST include all 5 news items with their respective emojis"""
            logger.warning("prompt_config module not available, using fallback prompt")

        logger.info("Generating cricket news content using Gemini API with NewsAPI data")

        # Generate content using Gemini API
        content = gemini_api.generate_content(enhanced_prompt)

        if content and not content.startswith("Error:"):
            logger.info("Cricket news content generated successfully using Gemini API")
            return content
        else:
            logger.warning(f"Gemini API returned error: {content}")
            return get_fallback_cricket_content()

    except Exception as e:
        logger.error(f"Error generating cricket news with Gemini API: {str(e)}")
        return get_fallback_cricket_content()

def get_fallback_cricket_content() -> str:
    """
    Get fallback cricket news content when NewsAPI or Gemini API is not available.
    Uses the exact required format with proper emoji sequence.

    Returns:
        str: Fallback cricket news content in the required format
    """
    fallback_content = """🏏 *CRICKET NEWS AND UPDATES*

_Indian cricket team prepares for upcoming international series with intensive training sessions._ 🏆

_IPL franchises announce new player acquisitions and strategic partnerships for next season._ 🎯

_Young cricketers showcase exceptional talent in domestic tournaments across the country._ 🔥

_Cricket stadiums gear up for exciting matches with enhanced facilities and fan experiences._ ⚡

_Former cricket legends share insights and mentor next generation of promising players._ 🌟"""

    logger.info("Using fallback cricket content with required format")
    return fallback_content

def generate_cricket_news_content(custom_prompt=None) -> str:
    """
    Generate cricket news content for posting.

    This is the main function called by the content posting system.
    It uses Gemini API to generate fresh cricket news content.

    Args:
        custom_prompt (str): Custom prompt for this project

    Returns:
        str: Formatted cricket news content ready for posting
    """
    try:
        logger.info("Generating cricket news content")

        # Generate content using Gemini API
        content = generate_cricket_news_with_gemini(custom_prompt)

        # Ensure content doesn't exceed Telegram's caption limit (1024 characters)
        if len(content) > 1024:
            content = content[:1021] + "..."
            logger.warning("Cricket news content truncated to fit Telegram limit")

        logger.info("Cricket news content generated successfully")
        return content

    except Exception as e:
        logger.error(f"Error generating cricket news content: {str(e)}")
        return get_fallback_cricket_content()

# Legacy function maintained for backward compatibility
def fetch_cricket_news(limit: int = 8):
    """
    Legacy function maintained for backward compatibility.
    Now redirects to Gemini-based content generation.

    Args:
        limit: Number of articles to fetch (ignored in new implementation)

    Returns:
        str: Generated cricket news content
    """
    logger.info("Legacy fetch_cricket_news called, redirecting to Gemini-based generation")
    return generate_cricket_news_content()

# Legacy function maintained for backward compatibility
def format_cricket_news_for_telegram(articles):
    """
    Legacy function maintained for backward compatibility.
    Now redirects to Gemini-based content generation.

    Args:
        articles: List of cricket news articles (ignored in new implementation)

    Returns:
        str: Generated cricket news content
    """
    logger.info("Legacy format_cricket_news_for_telegram called, redirecting to Gemini-based generation")
    return generate_cricket_news_content()
