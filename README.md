# Telegram Auto-Posting Bot

A Telegram bot that can automatically post different types of content to different Telegram channels.

## Features

- Add multiple Telegram channels
- Create auto-posting projects
- Configure different content types for each channel
- Manage and monitor your auto-posting projects

## Commands

- `/start` - Introduction to the bot
- `/addchannel` - Add a new channel by forwarding a message
- `/channels` - List all added channels
- `/addprojects` - Create a new auto-posting project
- `/projects` - Manage your existing projects

## Setup

1. Clone this repository
2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```
3. Create a `.env` file based on `.env.example` and add your Telegram bot token
4. Run the bot:
   ```
   python bot.py
   ```

## Getting a Telegram Bot Token

1. Open Telegram and search for [@BotFather](https://t.me/BotFather)
2. Start a chat and send `/newbot`
3. Follow the instructions to create a new bot
4. Copy the token provided by BotFather and add it to your `.env` file

## Adding Channels

To add a channel to the bot:

1. Make sure the bot is an admin of the channel you want to add
2. Send the `/addchannel` command to the bot
3. Forward any message from the channel to the bot

## Creating Auto-Posting Projects

To create a new auto-posting project:

1. Send the `/addprojects` command to the bot
2. Select a channel from the list
3. Choose the type of content you want to post
4. Enter a name for your project

## Managing Projects

To manage your auto-posting projects:

1. Send the `/projects` command to the bot
2. Select a project from the list
3. Use the available options to toggle status, edit, or delete the project

## Future Enhancements

- Scheduling posts at specific times
- Support for more content types
- Analytics and reporting
- Multiple channels per project
- Content approval workflow
