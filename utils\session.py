"""
User session management utilities.
"""
from typing import Dict, Any, Optional

# User session storage
user_sessions = {}

def get_user_session(user_id: int) -> Dict[str, Any]:
    """
    Get a user's session data, creating it if it doesn't exist.
    
    Args:
        user_id: The user's ID
        
    Returns:
        Dict: The user's session data
    """
    if user_id not in user_sessions:
        user_sessions[user_id] = {}
    return user_sessions[user_id]

def set_session_data(user_id: int, key: str, value: Any) -> None:
    """
    Set a value in a user's session.
    
    Args:
        user_id: The user's ID
        key: The key to set
        value: The value to set
    """
    session = get_user_session(user_id)
    session[key] = value

def get_session_data(user_id: int, key: str, default: Any = None) -> Any:
    """
    Get a value from a user's session.
    
    Args:
        user_id: The user's ID
        key: The key to get
        default: The default value to return if the key doesn't exist
        
    Returns:
        Any: The value from the session, or the default if not found
    """
    session = get_user_session(user_id)
    return session.get(key, default)

def clear_session(user_id: int) -> None:
    """
    Clear a user's session data.
    
    Args:
        user_id: The user's ID
    """
    if user_id in user_sessions:
        del user_sessions[user_id]

def clear_session_data(user_id: int, key: str) -> None:
    """
    Clear a specific key from a user's session.
    
    Args:
        user_id: The user's ID
        key: The key to clear
    """
    session = get_user_session(user_id)
    if key in session:
        del session[key]
