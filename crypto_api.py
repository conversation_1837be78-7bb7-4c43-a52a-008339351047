import logging
import requests
import json
import time
from datetime import datetime

logger = logging.getLogger(__name__)

# CoinGecko API endpoints
COINGECKO_API_URL = "https://api.coingecko.com/api/v3"
COINGECKO_COINS_ENDPOINT = f"{COINGECKO_API_URL}/coins/markets"
COINGECKO_TRENDING_ENDPOINT = f"{COINGECKO_API_URL}/search/trending"

# List of main coins to track
MAIN_COINS = ["bitcoin", "ethereum", "solana", "the-open-network"]

def get_crypto_prices():
    """
    Get cryptocurrency prices using CoinGecko API.

    Returns:
        dict: A dictionary containing crypto price data
    """
    try:
        # Get main coin data
        main_coins_data = get_main_coins_data()

        # Get trending coins
        trending_coin = get_trending_coin()

        # Get top gainers and losers
        top_gainer, top_loser = get_top_gainers_and_losers()

        # Combine all data
        crypto_data = {
            "main_coins": main_coins_data,
            "trending": trending_coin,
            "top_gainer": top_gainer,
            "top_loser": top_loser
        }

        logger.info(f"Successfully fetched crypto data: {len(main_coins_data)} main coins")
        return crypto_data

    except Exception as e:
        logger.error(f"Error fetching crypto prices: {e}")
        return get_fallback_crypto_data()

def get_main_coins_data():
    """
    Get data for the main coins.

    Returns:
        list: List of dictionaries with coin data
    """
    try:
        # Fetch data for main coins
        params = {
            "ids": ",".join(MAIN_COINS),
            "vs_currency": "usd",
            "order": "market_cap_desc",
            "per_page": 100,
            "page": 1,
            "sparkline": False,
            "price_change_percentage": "24h"
        }

        response = requests.get(COINGECKO_COINS_ENDPOINT, params=params, timeout=10)

        if response.status_code == 200:
            coins_data = response.json()

            # Format the data
            formatted_coins = []
            for coin in coins_data:
                symbol = coin.get("symbol", "").upper()
                price = f"${coin.get('current_price', 0):,.2f}"
                change_24h = f"{coin.get('price_change_percentage_24h', 0):.2f}%"

                # Add plus sign for positive changes
                if not change_24h.startswith("-") and not change_24h.startswith("0.00"):
                    change_24h = f"+{change_24h}"

                formatted_coins.append({
                    "symbol": symbol,
                    "price": price,
                    "change_24h": change_24h
                })

            return formatted_coins
        else:
            logger.error(f"Failed to fetch main coins: {response.status_code}")
            return []

    except Exception as e:
        logger.error(f"Error fetching main coins: {e}")
        return []

def get_trending_coin():
    """
    Get the top trending coin from CoinGecko.

    Returns:
        dict: Dictionary with trending coin data
    """
    try:
        response = requests.get(COINGECKO_TRENDING_ENDPOINT, timeout=10)

        if response.status_code == 200:
            trending_data = response.json()

            # Get the first trending coin
            if trending_data.get("coins") and len(trending_data["coins"]) > 0:
                trending_coin = trending_data["coins"][0]["item"]

                # Get more details about this coin
                coin_id = trending_coin.get("id")
                if coin_id:
                    coin_details = get_coin_details(coin_id)
                    if coin_details:
                        return coin_details

                # If we couldn't get details, return basic info
                return {
                    "symbol": trending_coin.get("symbol", "").upper(),
                    "price": "$?.???",  # We don't have price in trending endpoint
                    "change_24h": "??.??%"  # We don't have change in trending endpoint
                }

        logger.error(f"Failed to fetch trending coins: {response.status_code}")
        return {"symbol": "TRENDING", "price": "$?.??", "change_24h": "??.??%"}

    except Exception as e:
        logger.error(f"Error fetching trending coin: {e}")
        return {"symbol": "TRENDING", "price": "$?.??", "change_24h": "??.??%"}

def get_top_gainers_and_losers():
    """
    Get top gainers and losers from CoinGecko.

    Returns:
        tuple: (top_gainer, top_loser) dictionaries
    """
    try:
        # Fetch top 100 coins by market cap
        params = {
            "vs_currency": "usd",
            "order": "market_cap_desc",
            "per_page": 100,
            "page": 1,
            "sparkline": False,
            "price_change_percentage": "24h"
        }

        response = requests.get(COINGECKO_COINS_ENDPOINT, params=params, timeout=10)

        if response.status_code == 200:
            coins_data = response.json()

            # Filter out coins with null price change
            valid_coins = [coin for coin in coins_data if coin.get("price_change_percentage_24h") is not None]

            # Sort by price change percentage
            gainers = sorted(valid_coins, key=lambda x: x.get("price_change_percentage_24h", 0), reverse=True)
            losers = sorted(valid_coins, key=lambda x: x.get("price_change_percentage_24h", 0))

            # Get top gainer
            top_gainer = {}
            if gainers and len(gainers) > 0:
                coin = gainers[0]
                symbol = coin.get("symbol", "").upper()
                price = f"${coin.get('current_price', 0):,.2f}"
                change_24h = f"+{coin.get('price_change_percentage_24h', 0):.2f}%"

                top_gainer = {
                    "symbol": symbol,
                    "price": price,
                    "change_24h": change_24h
                }
            else:
                top_gainer = {"symbol": "GAINER", "price": "$?.??", "change_24h": "+?.??%"}

            # Get top loser
            top_loser = {}
            if losers and len(losers) > 0:
                coin = losers[0]
                symbol = coin.get("symbol", "").upper()
                price = f"${coin.get('current_price', 0):,.2f}"
                change_24h = f"{coin.get('price_change_percentage_24h', 0):.2f}%"

                top_loser = {
                    "symbol": symbol,
                    "price": price,
                    "change_24h": change_24h
                }
            else:
                top_loser = {"symbol": "LOSER", "price": "$?.??", "change_24h": "-?.??%"}

            return top_gainer, top_loser
        else:
            logger.error(f"Failed to fetch top gainers and losers: {response.status_code}")
            return (
                {"symbol": "GAINER", "price": "$?.??", "change_24h": "+?.??%"},
                {"symbol": "LOSER", "price": "$?.??", "change_24h": "-?.??%"}
            )

    except Exception as e:
        logger.error(f"Error fetching top gainers and losers: {e}")
        return (
            {"symbol": "GAINER", "price": "$?.??", "change_24h": "+?.??%"},
            {"symbol": "LOSER", "price": "$?.??", "change_24h": "-?.??%"}
        )

def get_coin_details(coin_id):
    """
    Get detailed information about a specific coin.

    Args:
        coin_id (str): The coin ID

    Returns:
        dict: Dictionary with coin data
    """
    try:
        # Fetch data for the specific coin
        params = {
            "ids": coin_id,
            "vs_currency": "usd",
            "sparkline": False,
            "price_change_percentage": "24h"
        }

        response = requests.get(COINGECKO_COINS_ENDPOINT, params=params, timeout=10)

        if response.status_code == 200:
            coins_data = response.json()

            if coins_data and len(coins_data) > 0:
                coin = coins_data[0]
                symbol = coin.get("symbol", "").upper()
                price = f"${coin.get('current_price', 0):,.2f}"
                change_24h = f"{coin.get('price_change_percentage_24h', 0):.2f}%"

                # Add plus sign for positive changes
                if not change_24h.startswith("-") and not change_24h.startswith("0.00"):
                    change_24h = f"+{change_24h}"

                return {
                    "symbol": symbol,
                    "price": price,
                    "change_24h": change_24h
                }

        return None

    except Exception as e:
        logger.error(f"Error fetching coin details: {e}")
        return None

def get_fallback_crypto_data():
    """
    Provide fallback crypto data when API fails.

    Returns:
        dict: A dictionary containing fallback crypto price data
    """
    # Get current date for the fallback data
    current_date = datetime.now().strftime("%Y-%m-%d")

    return {
        "main_coins": [
            {"symbol": "BTC", "price": "$56,800", "change_24h": "-0.63%"},
            {"symbol": "ETH", "price": "$2,332", "change_24h": "-0.80%"},
            {"symbol": "SOL", "price": "$132.18", "change_24h": "-1.63%"},
            {"symbol": "TON", "price": "$5.24", "change_24h": "-1.30%"}
        ],
        "trending": {"symbol": "TON", "price": "$5.23", "change_24h": "-1.30%"},
        "top_gainer": {"symbol": "AAVE", "price": "$149.72", "change_24h": "*****%"},
        "top_loser": {"symbol": "WIF", "price": "$1.57", "change_24h": "-5.78%"},
        "_fallback_note": f"Fallback data used on {current_date} - API may be unavailable"
    }

def format_crypto_post(crypto_data):
    """
    Format crypto data into a Telegram post.

    Args:
        crypto_data (dict): Cryptocurrency price data

    Returns:
        str: Formatted post text
    """
    today_date = datetime.now().strftime("%d-%m-%Y")

    # Create a bold title
    post = f"💰 *DAILY CRYPTO UPDATE: {today_date}*\n\n"

    # Add main coins with better formatting
    post += "_Major Cryptocurrencies:_\n\n"

    coin_emojis = {"BTC": "🟠", "ETH": "🔷", "SOL": "🟣", "TON": "💎"}

    for coin in crypto_data.get("main_coins", []):
        symbol = coin['symbol']
        emoji = coin_emojis.get(symbol, "🪙")
        price = coin['price']
        change = coin['change_24h']

        # Add color indicators for price changes
        if change.startswith("+"):
            change_indicator = "📈"
        elif change.startswith("-"):
            change_indicator = "📉"
        else:
            change_indicator = "➖"

        post += f"- _{emoji} {symbol}: {price} | 24h: {change} {change_indicator}_\n\n"

    # Add market overview section
    post += "_Market Highlights:_\n\n"

    # Add trending coin with better formatting
    trending = crypto_data.get("trending", {})
    post += f"- _🔥 Trending: #{trending.get('symbol', 'UNKNOWN')} at {trending.get('price', '$?.??')} | 24h: {trending.get('change_24h', '?.??%')}_\n\n"

    # Add top gainer with better formatting
    gainer = crypto_data.get("top_gainer", {})
    post += f"- _📈 Top Gainer: #{gainer.get('symbol', 'UNKNOWN')} at {gainer.get('price', '$?.??')} | 24h: {gainer.get('change_24h', '+?.??%')}_\n\n"

    # Add top loser with better formatting
    loser = crypto_data.get("top_loser", {})
    post += f"- _📉 Top Loser: #{loser.get('symbol', 'UNKNOWN')} at {loser.get('price', '$?.??')} | 24h: {loser.get('change_24h', '-?.??%')}_\n\n"

    # Add a note if this is fallback data
    if crypto_data.get("_fallback_note"):
        post += f"_Note: Using estimated data - live API unavailable_"

    return post

def generate_crypto_post():
    """
    Generate a formatted crypto price post.

    Returns:
        str: Formatted post text
    """
    crypto_data = get_crypto_prices()
    return format_crypto_post(crypto_data)
