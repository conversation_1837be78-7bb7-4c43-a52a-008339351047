"""
Time and timezone handlers.
"""
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ConversationHandler

import database as db
import timezone_utils as tz
from utils.session import get_session_data, set_session_data, clear_session

# Enable logging
logger = logging.getLogger(__name__)

# Conversation states
ENTERING_CUSTOM_TIME = 8

def handle_custom_post_time(update: Update, context: CallbackContext) -> int:
    """Handle custom time input for a project."""
    query = update.callback_query
    query.answer()
    
    # Add debug logging
    logging.info(f"handle_custom_post_time called with data: {query.data}")
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Store project_id in user session
    user_id = query.from_user.id
    set_session_data(user_id, 'project_id', project_id)
    set_session_data(user_id, 'action', 'set_post_time')
    
    # Add more debug logging
    logging.info(f"User session updated for user {user_id}, project_id: {project_id}")
    
    query.edit_message_text(
        "⏱️ *Enter Custom Posting Time*\n\n"
        "Please enter the time in 24-hour format (HH:MM).\n"
        "For example: `21:30` for 9:30 PM or `08:15` for 8:15 AM.",
        parse_mode="Markdown"
    )
    
    return ENTERING_CUSTOM_TIME

def handle_custom_time_input(update: Update, context: CallbackContext) -> int:
    """Handle custom time input."""
    user_id = update.effective_user.id
    time_text = update.message.text.strip()
    
    # Parse time input
    hour = 0
    minute = 0
    
    try:
        # Check if input contains a colon (HH:MM format)
        if ':' in time_text:
            parts = time_text.split(':')
            if len(parts) != 2:
                raise ValueError("Invalid time format")
                
            hour = int(parts[0])
            minute = int(parts[1])
            
            # Validate hour and minute
            if hour < 0 or hour > 23 or minute < 0 or minute > 59:
                raise ValueError("Invalid time range")
        else:
            # Just hour format
            hour = int(time_text)
            if hour < 0 or hour > 23:
                raise ValueError("Invalid hour range")
    except ValueError as e:
        update.message.reply_text(
            "⚠️ Invalid time format. Please enter time as HH:MM (e.g., 21:30) or just the hour (e.g., 21)."
        )
        return ENTERING_CUSTOM_TIME
    
    # Get project_id from user session
    project_id = get_session_data(user_id, 'project_id')
    
    if not project_id:
        update.message.reply_text("Session expired. Please start over.")
        return ConversationHandler.END
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        update.message.reply_text("Project not found. It may have been deleted.")
        return ConversationHandler.END
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    if not content_type:
        update.message.reply_text("Content type not found for this project.")
        return ConversationHandler.END
    
    # Update posting time in the appropriate content settings
    content_settings = project_info.get('content_settings', {})
    time_str = f"{hour:02d}:{minute:02d}" if minute > 0 else f"{hour}:00"
    
    if content_type in content_settings:
        settings = content_settings[content_type]
        settings["post_time_hour"] = hour
        settings["post_time_minute"] = minute
        db.update_project_content_settings(user_id, project_id, content_type, settings)
    
    # Send confirmation
    keyboard = [
        [InlineKeyboardButton(
            "⬅️ Back to Project Settings",
            callback_data=f"project_settings:{project_id}"
        )]
    ]
    
    update.message.reply_text(
        f"✅ Posting time updated to {time_str}",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
    
    # Clear session data
    clear_session(user_id)
    
    return ConversationHandler.END

def handle_set_frequency(update: Update, context: CallbackContext) -> None:
    """Handle setting the posting frequency."""
    query = update.callback_query
    query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info for this user
    user_id = query.from_user.id
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    if not content_type:
        query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return
    
    # Get content settings
    content_settings = project_info.get('content_settings', {})
    
    # Get current posting interval
    post_interval_hours = 24  # Default
    
    if content_type in content_settings:
        settings = content_settings[content_type]
        post_interval_hours = settings.get('post_interval_hours', 24)
    
    # Create frequency selection keyboard
    keyboard = []
    
    # Common intervals
    intervals = [
        ("Every 12 hours", 12),
        ("Daily (24 hours)", 24),
        ("Every 2 days", 48),
        ("Every 3 days", 72),
        ("Weekly", 168)
    ]
    
    for label, hours in intervals:
        # Add checkmark to current interval
        if hours == post_interval_hours:
            label = f"✅ {label}"
        
        keyboard.append([
            InlineKeyboardButton(label, callback_data=f"save_frequency:{project_id}:{hours}")
        ])
    
    # Back button
    keyboard.append([
        InlineKeyboardButton("⬅️ Back", callback_data=f"project_settings:{project_id}")
    ])
    
    query.edit_message_text(
        f"🔄 *Set Posting Frequency*\n\n"
        f"Current frequency: *{format_interval(post_interval_hours)}*\n\n"
        f"Select a new posting frequency:",
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_save_frequency(update: Update, context: CallbackContext) -> None:
    """Handle saving the posting frequency."""
    query = update.callback_query
    query.answer()
    
    # Extract project_id and hours from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    hours = int(parts[2])
    
    # Get project info for this user
    user_id = query.from_user.id
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    if not content_type:
        query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return
    
    # Get content settings
    content_settings = project_info.get('content_settings', {})
    
    # Update posting interval
    if content_type in content_settings:
        settings = content_settings[content_type]
        settings["post_interval_hours"] = hours
        db.update_project_content_settings(user_id, project_id, content_type, settings)
    
    # Show updated project settings
    from handlers.project_handlers import handle_project_settings
    handle_project_settings(update, context)

def format_interval(hours: int) -> str:
    """Format an interval in hours to a human-readable string."""
    if hours == 12:
        return "Every 12 hours"
    elif hours == 24:
        return "Daily (24 hours)"
    elif hours == 48:
        return "Every 2 days"
    elif hours == 72:
        return "Every 3 days"
    elif hours == 168:
        return "Weekly"
    else:
        return f"Every {hours} hours"

def handle_reset_last_posted(update: Update, context: CallbackContext) -> None:
    """Handle resetting the last posted time."""
    query = update.callback_query
    query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    if not content_type:
        query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return
    
    # Reset last posted time
    success = db.reset_last_posted_time(project_id, content_type)
    
    # Create keyboard to go back to project settings
    keyboard = [
        [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
    ]
    
    if success:
        query.edit_message_text(
            "✅ Last posted time has been reset.\n\n"
            "The next post will be made at the scheduled time regardless of when the last post was made.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        query.edit_message_text(
            "❌ Failed to reset last posted time.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
