import logging
import pytz
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Time zone mapping for different countries
COUNTRY_TIMEZONES = {
    "India": "Asia/Kolkata",
    "USA": "America/New_York",
    "Russia": "Europe/Moscow",
    "UK": "Europe/London",
    "Japan": "Asia/Tokyo",
    "Australia": "Australia/Sydney",
    "Germany": "Europe/Berlin",
    "Brazil": "America/Sao_Paulo",
    "Canada": "America/Toronto",
    "China": "Asia/Shanghai"
}

# Default country
DEFAULT_COUNTRY = "India"

def get_timezone_for_country(country):
    """
    Get the timezone for a specific country.

    Args:
        country (str): Country name

    Returns:
        pytz.timezone: Timezone object for the country
    """
    timezone_str = COUNTRY_TIMEZONES.get(country, COUNTRY_TIMEZONES[DEFAULT_COUNTRY])
    return pytz.timezone(timezone_str)

def get_current_time_for_country(country):
    """
    Get the current time for a specific country.

    Args:
        country (str): Country name

    Returns:
        datetime: Current time in the country's timezone
    """
    tz = get_timezone_for_country(country)
    return datetime.now(tz)

def format_time_for_country(country, time_format="%Y-%m-%d %H:%M:%S"):
    """
    Get formatted current time for a specific country.

    Args:
        country (str): Country name
        time_format (str): Time format string

    Returns:
        str: Formatted time string
    """
    current_time = get_current_time_for_country(country)
    return current_time.strftime(time_format)

def is_posting_time(scheduled_hour, country, last_posted=None, min_hours_between_posts=20, scheduled_minute=0, bypass_time_check=False):
    """
    Check if it's time to post based on the country's timezone.

    Args:
        scheduled_hour (int): Hour of the day to post (0-23)
        country (str): Country name for timezone
        last_posted (str): ISO format datetime string of last post
        min_hours_between_posts (int): Minimum hours between posts
        scheduled_minute (int): Minute of the hour to post (0-59)
        bypass_time_check (bool): If True, bypass the time check and always return True

    Returns:
        bool: True if it's time to post, False otherwise
    """
    # If bypass_time_check is True, always return True
    if bypass_time_check:
        logger.info("Bypassing time check - manual post or force post")
        return True

    # Get current time in the country's timezone
    tz = get_timezone_for_country(country)
    current_time = datetime.now(tz)
    current_hour = current_time.hour
    current_minute = current_time.minute

    # Check if it's the scheduled hour and minute
    if current_hour == scheduled_hour and current_minute == scheduled_minute:
        # If no previous post, it's time to post
        if not last_posted:
            return True

        # Check if enough time has passed since last post
        try:
            # Convert last_posted string to datetime with timezone
            last_post_time = datetime.fromisoformat(last_posted)
            if last_post_time.tzinfo is None:
                # If last_post_time has no timezone, assume it's in UTC
                last_post_time = pytz.utc.localize(last_post_time)

            # Convert to the country's timezone
            last_post_time = last_post_time.astimezone(tz)

            # Calculate time since last post
            time_since_post = current_time - last_post_time

            # Only post if it's been more than min_hours_between_posts hours
            if time_since_post.total_seconds() >= min_hours_between_posts * 3600:
                return True
            else:
                hours_to_wait = min_hours_between_posts - (time_since_post.total_seconds() / 3600)
                logger.info(f"Not enough time since last post. Wait {hours_to_wait:.1f} more hours.")
                return False

        except (ValueError, TypeError) as e:
            logger.warning(f"Error parsing last posted time: {e}")
            # If there's an error parsing the time, allow posting
            return True

    return False

def get_all_countries():
    """
    Get a list of all supported countries.

    Returns:
        list: List of country names
    """
    return list(COUNTRY_TIMEZONES.keys())

def get_time_difference(country1, country2):
    """
    Get the time difference between two countries in hours.

    Args:
        country1 (str): First country name
        country2 (str): Second country name

    Returns:
        float: Time difference in hours
    """
    tz1 = get_timezone_for_country(country1)
    tz2 = get_timezone_for_country(country2)

    time1 = datetime.now(tz1)
    time2 = datetime.now(tz2)

    # Calculate difference in hours
    diff_seconds = (time1.utcoffset().total_seconds() - time2.utcoffset().total_seconds())
    diff_hours = diff_seconds / 3600

    return diff_hours
