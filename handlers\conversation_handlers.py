"""
Conversation handler definitions.
"""
import logging
from telegram.ext import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CallbackQueryHandler,
    ConversationHandler,
    filters,
)

from handlers.command_handlers import cancel
from handlers.channel_handlers import (
    add_channel,
    handle_forwarded_message,
    handle_cancel_add_channel,
)
from handlers.project_handlers import (
    add_projects,
    handle_channel_selection,
    handle_content_type_selection,
    handle_project_name,
    handle_test_post,
)
from handlers.button_handlers import (
    handle_add_button,
    handle_button_text,
    handle_button_url,
)
from handlers.image_handlers import (
    handle_upload_image,
    handle_image_upload,
)
from handlers.time_handlers import (
    handle_custom_post_time,
    handle_custom_time_input,
)
from handlers.prompt_handlers import (
    handle_start_edit_prompt,
    handle_custom_prompt_input,
)

# Enable logging
logger = logging.getLogger(__name__)

# Conversation states
AWAITING_CHANNEL_FORWARD = 1
SELECTING_CHANNEL = 2
SELECTING_CONTENT_TYPE = 3
ENTERING_PROJECT_NAME = 4
ENTERING_BUTTON_TEXT = 5
ENTERING_BUTTON_URL = 6
UPLOADING_IMAGE = 7
ENTERING_CUSTOM_TIME = 8
ENTERING_CUSTOM_PROMPT = 9

def get_add_channel_conversation_handler():
    """Get the conversation handler for adding channels."""
    return ConversationHandler(
        entry_points=[
            CommandHandler("addchannel", add_channel),
            CallbackQueryHandler(add_channel, pattern=r"^cmd_addchannel$"),
        ],
        states={
            AWAITING_CHANNEL_FORWARD: [
                MessageHandler(filters.ALL & ~filters.COMMAND, handle_forwarded_message),
                CallbackQueryHandler(handle_cancel_add_channel, pattern=r"^cancel_add_channel$"),
                CommandHandler("cancel", cancel)
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)],
        per_message=False,
        per_chat=True,
        per_user=True,
    )

def get_add_project_conversation_handler():
    """Get the conversation handler for adding projects."""
    return ConversationHandler(
        entry_points=[
            CommandHandler("addprojects", add_projects),
            CallbackQueryHandler(add_projects, pattern=r"^cmd_addprojects$"),
        ],
        states={
            SELECTING_CHANNEL: [
                CallbackQueryHandler(handle_channel_selection, pattern=r"^select_channel:")
            ],
            SELECTING_CONTENT_TYPE: [
                CallbackQueryHandler(handle_content_type_selection, pattern=r"^content_type:")
            ],
            ENTERING_PROJECT_NAME: [
                MessageHandler(filters.TEXT & ~filters.COMMAND, handle_project_name)
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)],
        per_message=False,
        per_chat=True,
        per_user=True,
    )

def get_custom_time_conversation_handler():
    """Get the conversation handler for custom time input."""
    return ConversationHandler(
        entry_points=[
            CallbackQueryHandler(handle_custom_post_time, pattern=r"^custom_post_time:")
        ],
        states={
            ENTERING_CUSTOM_TIME: [
                MessageHandler(filters.TEXT & ~filters.COMMAND, handle_custom_time_input)
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)],
        name="custom_time_conversation",
        per_message=False,
        per_chat=True,
        per_user=True,
    )

def get_add_button_conversation_handler():
    """Get the conversation handler for adding buttons."""
    return ConversationHandler(
        entry_points=[
            CallbackQueryHandler(handle_add_button, pattern=r"^add_button:")
        ],
        states={
            ENTERING_BUTTON_TEXT: [
                MessageHandler(filters.TEXT & ~filters.COMMAND, handle_button_text)
            ],
            ENTERING_BUTTON_URL: [
                MessageHandler(filters.TEXT & ~filters.COMMAND, handle_button_url)
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)],
        per_message=False,
        per_chat=True,
        per_user=True,
    )

def get_upload_image_conversation_handler():
    """Get the conversation handler for uploading images."""
    return ConversationHandler(
        entry_points=[
            CallbackQueryHandler(handle_upload_image, pattern=r"^upload_image:")
        ],
        states={
            UPLOADING_IMAGE: [
                MessageHandler(filters.PHOTO, handle_image_upload),
                MessageHandler(
                    filters.TEXT & ~filters.COMMAND,
                    lambda update, _: update.message.reply_text("Please send an image, not text.")
                )
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)],
        per_message=False,
        per_chat=True,
        per_user=True,
    )

def get_edit_prompt_conversation_handler():
    """Get the conversation handler for editing custom prompts."""
    return ConversationHandler(
        entry_points=[
            CallbackQueryHandler(handle_start_edit_prompt, pattern=r"^start_edit_prompt:")
        ],
        states={
            ENTERING_CUSTOM_PROMPT: [
                MessageHandler(filters.TEXT & ~filters.COMMAND, handle_custom_prompt_input),
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)],
        per_message=False,
        per_chat=True,
        per_user=True,
    )
