from PIL import Image, ImageDraw, ImageFont
import os

def create_daily_news_image():
    # Create directory if it doesn't exist
    os.makedirs('images', exist_ok=True)
    
    # Create a new image with blue background
    width, height = 1200, 630
    img = Image.new('RGB', (width, height), color=(53, 88, 151))
    
    # Get a drawing context
    d = ImageDraw.Draw(img)
    
    # Try to use a system font, fall back to default if not available
    try:
        # Try different common fonts
        font_paths = [
            "arial.ttf",
            "Arial.ttf",
            "C:\\Windows\\Fonts\\arial.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
            "/System/Library/Fonts/Helvetica.ttc"
        ]
        
        title_font = None
        for font_path in font_paths:
            try:
                title_font = ImageFont.truetype(font_path, 60)
                break
            except IOError:
                continue
        
        if title_font is None:
            title_font = ImageFont.load_default()
            
    except Exception:
        title_font = ImageFont.load_default()
    
    # Add title
    title_text = "📰 DAILY NEWS SUMMARY"
    title_width = d.textlength(title_text, font=title_font)
    d.text(((width - title_width) // 2, 100), title_text, fill=(255, 255, 255), font=title_font)
    
    # Add decorative elements
    d.rectangle([(100, 200), (width - 100, 205)], fill=(255, 255, 255))
    
    # Add current date
    try:
        date_font = ImageFont.truetype("arial.ttf", 40)
    except:
        date_font = ImageFont.load_default()
    
    import datetime
    current_date = datetime.datetime.now().strftime("%A, %B %d, %Y")
    date_width = d.textlength(current_date, font=date_font)
    d.text(((width - date_width) // 2, 250), current_date, fill=(255, 255, 255), font=date_font)
    
    # Add bottom decorative element
    d.rectangle([(100, height - 150), (width - 100, height - 145)], fill=(255, 255, 255))
    
    # Save the image
    img.save('images/daily_news_summary.png')
    print("Image created successfully at images/daily_news_summary.png")

if __name__ == "__main__":
    create_daily_news_image()
