"""
Keyboard creation utilities.
"""
from typing import List, Dict, Any
from telegram import InlineKeyboardButton, InlineKeyboardMarkup

def create_main_menu_keyboard() -> InlineKeyboardMarkup:
    """
    Create the main menu keyboard.
    
    Returns:
        InlineKeyboardMarkup: The main menu keyboard
    """
    keyboard = [
        [InlineKeyboardButton("📱 Add Channel", callback_data="cmd_addchannel")],
        [InlineKeyboardButton("📋 View Channels", callback_data="cmd_channels")],
        [InlineKeyboardButton("📊 Add Project", callback_data="cmd_addprojects")],
        [InlineKeyboardButton("⚙️ Manage Projects", callback_data="cmd_projects")]
    ]
    return InlineKeyboardMarkup(keyboard)

def create_channel_selection_keyboard(channels: Dict[str, Dict[str, Any]]) -> InlineKeyboardMarkup:
    """
    Create a keyboard for channel selection.
    
    Args:
        channels: Dictionary of channels
        
    Returns:
        InlineKeyboardMarkup: The channel selection keyboard
    """
    keyboard = []
    
    # Add a button for each channel
    for channel_id, channel_info in channels.items():
        channel_name = channel_info.get('name', 'Unknown')
        keyboard.append([
            InlineKeyboardButton(
                f"📢 {channel_name}",
                callback_data=f"select_channel:{channel_id}"
            )
        ])
    
    # Add cancel button
    keyboard.append([InlineKeyboardButton("❌ Cancel", callback_data="cmd_cancel")])
    
    return InlineKeyboardMarkup(keyboard)

def create_content_type_keyboard() -> InlineKeyboardMarkup:
    """
    Create a keyboard for content type selection.

    Returns:
        InlineKeyboardMarkup: The content type keyboard
    """
    keyboard = [
        [InlineKeyboardButton("📰 DAILY NEWS SUMMARY", callback_data="content_type:daily_news_summary")],
        [InlineKeyboardButton("💰 CRYPTO PRICES", callback_data="content_type:crypto_prices")],
        [InlineKeyboardButton("💪 HEALTH & FITNESS", callback_data="content_type:health_fitness")],
        [InlineKeyboardButton("🏏 CRICKET NEWS AND UPDATES", callback_data="content_type:cricket_news")],
        [InlineKeyboardButton("❌ Cancel", callback_data="cmd_cancel")]
    ]
    return InlineKeyboardMarkup(keyboard)

def create_project_settings_keyboard(project_id: str, is_active: bool) -> InlineKeyboardMarkup:
    """
    Create a keyboard for project settings.

    Args:
        project_id: The project ID
        is_active: Whether the project is active

    Returns:
        InlineKeyboardMarkup: The project settings keyboard
    """
    toggle_text = "🔴 Deactivate Project" if is_active else "🟢 Activate Project"

    keyboard = [
        [InlineKeyboardButton("⏱️ Set Posting Time", callback_data=f"set_post_time:{project_id}")],
        [InlineKeyboardButton("🔄 Set Posting Frequency", callback_data=f"set_frequency:{project_id}")],
        [InlineKeyboardButton("🌐 Set Timezone", callback_data=f"set_timezone:{project_id}")],
        [InlineKeyboardButton("🖼️ Image Settings", callback_data=f"image_settings:{project_id}")],
        [InlineKeyboardButton("🔘 Custom Buttons", callback_data=f"custom_buttons:{project_id}")],
        [InlineKeyboardButton("🤖 Edit AI Prompt", callback_data=f"edit_prompt:{project_id}")],
        [InlineKeyboardButton("📝 Content Settings", callback_data=f"content_settings:{project_id}")],
        [InlineKeyboardButton("📊 View History", callback_data=f"view_history:{project_id}")],
        [InlineKeyboardButton("🔄 Reset Last Posted", callback_data=f"reset_last_posted:{project_id}")],
        [InlineKeyboardButton(toggle_text, callback_data=f"toggle_project:{project_id}")],
        [InlineKeyboardButton("🗑️ Delete Project", callback_data=f"delete_project:{project_id}")],
        [InlineKeyboardButton("📤 Manual Post", callback_data=f"manual_post:{project_id}")],
        [InlineKeyboardButton("⬅️ Back to Projects", callback_data="back_to_projects")]
    ]
    return InlineKeyboardMarkup(keyboard)

def create_timezone_keyboard() -> InlineKeyboardMarkup:
    """
    Create a keyboard for timezone selection.
    
    Returns:
        InlineKeyboardMarkup: The timezone keyboard
    """
    keyboard = [
        [InlineKeyboardButton("🇮🇳 India", callback_data="set_country:India")],
        [InlineKeyboardButton("🇺🇸 USA", callback_data="set_country:USA")],
        [InlineKeyboardButton("🇷🇺 Russia", callback_data="set_country:Russia")],
        [InlineKeyboardButton("🇬🇧 UK", callback_data="set_country:UK")],
        [InlineKeyboardButton("🇯🇵 Japan", callback_data="set_country:Japan")],
        [InlineKeyboardButton("🇦🇺 Australia", callback_data="set_country:Australia")],
        [InlineKeyboardButton("🇩🇪 Germany", callback_data="set_country:Germany")],
        [InlineKeyboardButton("❌ Cancel", callback_data="cmd_cancel")]
    ]
    return InlineKeyboardMarkup(keyboard)

def create_image_settings_keyboard(project_id: str, current_mode: str) -> InlineKeyboardMarkup:
    """
    Create a keyboard for image settings.
    
    Args:
        project_id: The project ID
        current_mode: The current image mode
        
    Returns:
        InlineKeyboardMarkup: The image settings keyboard
    """
    # Add checkmarks to show current selection
    default_text = "✅ Default Image" if current_mode == "default" else "Default Image"
    custom_text = "✅ Custom Image" if current_mode == "custom" else "Custom Image"
    internet_text = "✅ Internet Image" if current_mode == "internet" else "Internet Image"
    
    keyboard = [
        [InlineKeyboardButton(default_text, callback_data=f"set_image_mode:{project_id}:default")],
        [InlineKeyboardButton(custom_text, callback_data=f"set_image_mode:{project_id}:custom")],
        [InlineKeyboardButton(internet_text, callback_data=f"set_image_mode:{project_id}:internet")],
    ]
    
    # Add upload button if custom mode is selected
    if current_mode == "custom":
        keyboard.append([InlineKeyboardButton("📤 Upload Custom Image", callback_data=f"upload_image:{project_id}")])
        keyboard.append([InlineKeyboardButton("🔄 Reset Custom Image", callback_data=f"reset_image:{project_id}")])
    
    # Add back button
    keyboard.append([InlineKeyboardButton("⬅️ Back to Settings", callback_data=f"project_settings:{project_id}")])
    
    return InlineKeyboardMarkup(keyboard)

def create_custom_buttons_keyboard(project_id: str, has_buttons: bool, buttons_enabled: bool) -> InlineKeyboardMarkup:
    """
    Create a keyboard for custom buttons settings.
    
    Args:
        project_id: The project ID
        has_buttons: Whether the project has custom buttons
        buttons_enabled: Whether custom buttons are enabled
        
    Returns:
        InlineKeyboardMarkup: The custom buttons keyboard
    """
    keyboard = [
        [InlineKeyboardButton("➕ Add Button", callback_data=f"add_button:{project_id}")],
    ]
    
    if has_buttons:
        toggle_text = "🔴 Disable Buttons" if buttons_enabled else "🟢 Enable Buttons"
        keyboard.append([InlineKeyboardButton(toggle_text, callback_data=f"toggle_buttons:{project_id}")])
    
    keyboard.append([InlineKeyboardButton("⬅️ Back to Settings", callback_data=f"project_settings:{project_id}")])
    
    return InlineKeyboardMarkup(keyboard)

def create_back_button(callback_data: str, text: str = "⬅️ Back") -> InlineKeyboardMarkup:
    """
    Create a keyboard with just a back button.
    
    Args:
        callback_data: The callback data for the back button
        text: The text for the back button
        
    Returns:
        InlineKeyboardMarkup: The back button keyboard
    """
    keyboard = [[InlineKeyboardButton(text, callback_data=callback_data)]]
    return InlineKeyboardMarkup(keyboard)
