#!/usr/bin/env python3
"""
Test script for the configurable prompt system.
"""

import os
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import prompt_config
    print("=== Prompt Configuration System Test ===")
    
    # Check if JSON file exists
    if os.path.exists(prompt_config.PROMPTS_JSON_FILE):
        print(f"✅ JSON configuration file found: {prompt_config.PROMPTS_JSON_FILE}")
    else:
        print(f"❌ JSON configuration file not found: {prompt_config.PROMPTS_JSON_FILE}")
    
    # Validate prompts
    status = prompt_config.validate_prompts()
    print("\nPrompt Status:")
    for content_type, prompt_status in status.items():
        emoji = "✅" if prompt_status != "missing" else "❌"
        print(f"  {emoji} {content_type}: {prompt_status}")
    
    # Test getting prompts
    print("\n=== Sample Prompts ===")
    
    print("\n1. Daily News Prompt (first 200 chars):")
    daily_prompt = prompt_config.get_daily_news_prompt("India")
    print(daily_prompt[:200] + "..." if len(daily_prompt) > 200 else daily_prompt)
    
    print("\n2. Cricket News Prompt (first 200 chars):")
    cricket_prompt = prompt_config.get_cricket_news_prompt()
    print(cricket_prompt[:200] + "..." if len(cricket_prompt) > 200 else cricket_prompt)
    
    print("\n3. Health & Fitness Prompt (first 200 chars):")
    health_prompt = prompt_config.get_health_fitness_prompt()
    print(health_prompt[:200] + "..." if len(health_prompt) > 200 else health_prompt)
    
    print("\n4. Crypto Prices Prompt (first 200 chars):")
    crypto_prompt = prompt_config.get_crypto_prices_prompt()
    print(crypto_prompt[:200] + "..." if len(crypto_prompt) > 200 else crypto_prompt)
    
    print("\n=== Configuration Summary ===")
    json_prompts = prompt_config.load_prompts_from_json()
    print(f"JSON prompts loaded: {len(json_prompts)}")
    print(f"Default prompts available: {len(prompt_config.DEFAULT_PROMPTS)}")

    # Test prompt generation for all content types
    print("\n=== Testing All Content Types ===")

    content_types = [
        ("Daily News", lambda: prompt_config.get_daily_news_prompt("India")),
        ("Cricket News", lambda: prompt_config.get_cricket_news_prompt()),
        ("Health & Fitness", lambda: prompt_config.get_health_fitness_prompt()),
        ("Crypto Prices", lambda: prompt_config.get_crypto_prices_prompt())
    ]

    all_working = True
    for name, func in content_types:
        try:
            prompt = func()
            if prompt and not prompt.startswith("Error:"):
                print(f"✅ {name}: Prompt generated successfully ({len(prompt)} chars)")
            else:
                print(f"❌ {name}: Failed to generate prompt")
                all_working = False
        except Exception as e:
            print(f"❌ {name}: Error - {str(e)}")
            all_working = False

    if all_working:
        print("\n🎉 All content types working correctly!")
        print("✅ Configurable prompt system is fully functional!")
    else:
        print("\n⚠️  Some content types have issues - check the errors above")

except Exception as e:
    print(f"❌ Error testing prompt configuration: {str(e)}")
    import traceback
    traceback.print_exc()
