import os
import json
import requests
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO
)
logger = logging.getLogger(__name__)

# Import prompt configuration
try:
    import prompt_config
except ImportError:
    prompt_config = None

# News API endpoints
NEWSAPI_API_KEY = os.getenv("NEWSAPI_API_KEY")
NEWSAPI_URL = "https://newsapi.org/v2/top-headlines"

# Gnews API as a backup
GNEWS_API_KEY = os.getenv("GNEWS_API_KEY")
GNEWS_URL = "https://gnews.io/api/v4/top-headlines"

# Country code mapping
COUNTRY_CODES = {
    "India": "in",
    "USA": "us",
    "UK": "gb",
    "Russia": "ru",
    "Australia": "au",
    "Canada": "ca",
    "China": "cn",
    "Japan": "jp",
    "Germany": "de",
    "France": "fr"
}

# Categories to fetch
CATEGORIES = ["general", "business", "sports", "technology", "entertainment"]

def get_latest_news(country="India"):
    """
    Get the latest news for a specific country using NewsAPI.

    Args:
        country (str): The country to get news for

    Returns:
        list: A list of news articles
    """
    try:
        # Convert country name to country code
        country_code = COUNTRY_CODES.get(country, "in")  # Default to India if country not found

        # If we have a NewsAPI key, try that first
        if NEWSAPI_API_KEY:
            articles = get_news_from_newsapi(country_code)
            if articles and len(articles) >= 10:
                logger.info(f"Successfully fetched {len(articles)} articles from NewsAPI")
                return articles

        # If NewsAPI failed or we don't have a key, try GNews
        if GNEWS_API_KEY:
            articles = get_news_from_gnews(country_code)
            if articles and len(articles) >= 5:
                logger.info(f"Successfully fetched {len(articles)} articles from GNews")
                return articles

        # If both APIs failed, use web scraping as a last resort
        articles = scrape_news(country)
        if articles and len(articles) >= 5:
            logger.info(f"Successfully scraped {len(articles)} articles")
            return articles

        # If all methods failed, return fallback news
        logger.warning("All news fetching methods failed, using fallback news")
        return get_fallback_news(country)

    except Exception as e:
        logger.error(f"Error fetching news: {e}")
        return get_fallback_news(country)

def get_news_from_newsapi(country_code):
    """
    Get news from NewsAPI.

    Args:
        country_code (str): The country code

    Returns:
        list: A list of news articles
    """
    try:
        all_articles = []

        # Fetch news from different categories to get a good mix
        for category in CATEGORIES:
            params = {
                "country": country_code,
                "category": category,
                "pageSize": 5,  # Get 5 articles per category
                "apiKey": NEWSAPI_API_KEY
            }

            response = requests.get(NEWSAPI_URL, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "ok" and data.get("articles"):
                    # Add category to each article for better organization
                    for article in data["articles"]:
                        article["category"] = category
                        # Add timestamp for sorting
                        if article.get("publishedAt"):
                            article["timestamp"] = datetime.fromisoformat(article["publishedAt"].replace("Z", "+00:00"))
                        else:
                            article["timestamp"] = datetime.now()

                    all_articles.extend(data["articles"])

        # Sort articles by timestamp (newest first)
        all_articles.sort(key=lambda x: x.get("timestamp", datetime.now()), reverse=True)

        # Remove duplicates (based on title)
        unique_articles = []
        seen_titles = set()

        for article in all_articles:
            title = article.get("title", "").lower()
            # Skip articles with no title or already seen titles
            if not title or title in seen_titles:
                continue

            seen_titles.add(title)
            unique_articles.append(article)

        return unique_articles

    except Exception as e:
        logger.error(f"Error fetching news from NewsAPI: {e}")
        return []

def get_news_from_gnews(country_code):
    """
    Get news from GNews API.

    Args:
        country_code (str): The country code

    Returns:
        list: A list of news articles
    """
    try:
        all_articles = []

        # Fetch news from different topics to get a good mix
        for category in ["general", "business", "sports", "technology"]:
            params = {
                "country": country_code,
                "topic": category,
                "max": 5,  # Get 5 articles per category
                "apikey": GNEWS_API_KEY
            }

            response = requests.get(GNEWS_URL, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get("articles"):
                    # Add category to each article for better organization
                    for article in data["articles"]:
                        article["category"] = category
                        # Add timestamp for sorting
                        if article.get("publishedAt"):
                            article["timestamp"] = datetime.fromisoformat(article["publishedAt"].replace("Z", "+00:00"))
                        else:
                            article["timestamp"] = datetime.now()

                    all_articles.extend(data["articles"])

        # Sort articles by timestamp (newest first)
        all_articles.sort(key=lambda x: x.get("timestamp", datetime.now()), reverse=True)

        # Remove duplicates (based on title)
        unique_articles = []
        seen_titles = set()

        for article in all_articles:
            title = article.get("title", "").lower()
            # Skip articles with no title or already seen titles
            if not title or title in seen_titles:
                continue

            seen_titles.add(title)
            unique_articles.append(article)

        return unique_articles

    except Exception as e:
        logger.error(f"Error fetching news from GNews: {e}")
        return []

def scrape_news(country):
    """
    Scrape news from popular news websites.
    This is a fallback method if the APIs fail.

    Args:
        country (str): The country to get news for

    Returns:
        list: A list of news articles
    """
    # This is a placeholder for web scraping implementation
    # In a real implementation, you would use BeautifulSoup or similar to scrape news websites

    # For now, return an empty list to indicate that scraping failed
    return []

def get_fallback_news(country):
    """
    Get fallback news when all other methods fail.

    Args:
        country (str): The country to get news for

    Returns:
        list: A list of fallback news articles
    """
    # Get current date for the fallback data
    current_date = datetime.now().strftime("%Y-%m-%d")

    # Create some generic news articles that don't mention specific events
    fallback_articles = []

    if country == "India":
        fallback_articles = [
            {
                "title": "Government Announces New Economic Measures",
                "description": "The Indian government has announced new economic measures aimed at boosting growth and creating jobs.",
                "category": "general",
                "timestamp": datetime.now()
            },
            {
                "title": "Stock Market Shows Positive Trends",
                "description": "Indian stock markets showed positive trends today with both Sensex and Nifty gaining points.",
                "category": "business",
                "timestamp": datetime.now()
            },
            {
                "title": "Cricket Team Prepares for Upcoming Series",
                "description": "The Indian cricket team is preparing for an upcoming international series with intensive training sessions.",
                "category": "sports",
                "timestamp": datetime.now()
            },
            {
                "title": "Tech Industry Sees Growth in Innovation",
                "description": "India's technology sector continues to see growth with new startups and innovation hubs emerging across the country.",
                "category": "technology",
                "timestamp": datetime.now()
            },
            {
                "title": "Cultural Festival Celebrates Regional Diversity",
                "description": "A major cultural festival celebrating India's regional diversity is being held with performances from across the country.",
                "category": "entertainment",
                "timestamp": datetime.now()
            }
        ]
    elif country == "USA":
        fallback_articles = [
            {
                "title": "Federal Reserve Reviews Economic Policy",
                "description": "The Federal Reserve is reviewing its economic policies in light of recent market developments.",
                "category": "general",
                "timestamp": datetime.now()
            },
            {
                "title": "Tech Stocks Show Strong Performance",
                "description": "Technology stocks on Wall Street showed strong performance with major companies reporting gains.",
                "category": "business",
                "timestamp": datetime.now()
            },
            {
                "title": "Major Sports League Announces Schedule",
                "description": "A major American sports league has announced its schedule for the upcoming season with several key matchups.",
                "category": "sports",
                "timestamp": datetime.now()
            },
            {
                "title": "New Technology Innovations Unveiled",
                "description": "Several new technology innovations were unveiled at a major tech conference in Silicon Valley.",
                "category": "technology",
                "timestamp": datetime.now()
            },
            {
                "title": "Entertainment Industry Adapts to New Trends",
                "description": "The American entertainment industry is adapting to new consumer trends with innovative content delivery.",
                "category": "entertainment",
                "timestamp": datetime.now()
            }
        ]
    else:
        # Generic fallback for other countries
        fallback_articles = [
            {
                "title": "Government Announces New Policies",
                "description": f"The government of {country} has announced new policies aimed at addressing current challenges.",
                "category": "general",
                "timestamp": datetime.now()
            },
            {
                "title": "Economic Indicators Show Mixed Results",
                "description": f"Economic indicators in {country} are showing mixed results with some sectors performing better than others.",
                "category": "business",
                "timestamp": datetime.now()
            },
            {
                "title": "Sports Teams Prepare for Competitions",
                "description": f"Sports teams from {country} are preparing for upcoming international competitions.",
                "category": "sports",
                "timestamp": datetime.now()
            },
            {
                "title": "Technology Sector Sees Innovation",
                "description": f"The technology sector in {country} continues to innovate with new developments in various fields.",
                "category": "technology",
                "timestamp": datetime.now()
            },
            {
                "title": "Cultural Events Highlight Traditions",
                "description": f"Cultural events in {country} are highlighting traditional arts and modern expressions.",
                "category": "entertainment",
                "timestamp": datetime.now()
            }
        ]

    # Add a note that these are fallback articles
    for article in fallback_articles:
        article["note"] = f"Fallback article generated on {current_date} - API may be unavailable"

    return fallback_articles

def sanitize_text(text):
    """
    Sanitize text for Telegram markdown formatting.

    Args:
        text (str): The text to sanitize

    Returns:
        str: Sanitized text
    """
    if not text:
        return ""

    # Replace characters that can break markdown formatting
    sanitized = text.replace('_', ' ').replace('*', '').replace('`', '')

    # Remove any other potentially problematic characters
    sanitized = sanitized.replace('\n', ' ').replace('\r', ' ')

    # Remove any non-ASCII characters that might cause issues
    sanitized = ''.join(c for c in sanitized if ord(c) < 128)

    return sanitized

def format_news_summary(articles, country):
    """
    Format news articles into a summary.

    Args:
        articles (list): A list of news articles
        country (str): The country the news is for

    Returns:
        str: Formatted news summary
    """
    # Get current date in a readable format
    current_date = datetime.now().strftime("%A, %B %d, %Y")

    # Start with the title
    summary = f"📰 *DAILY NEWS SUMMARY: {country} - {current_date}*\n\n"

    # Categorize articles
    general_news = [a for a in articles if a.get("category") == "general"]
    business_news = [a for a in articles if a.get("category") == "business"]
    sports_news = [a for a in articles if a.get("category") == "sports"]
    tech_news = [a for a in articles if a.get("category") == "technology"]
    entertainment_news = [a for a in articles if a.get("category") == "entertainment"]

    # Add 3-4 major headlines (prioritize general news)
    headlines_added = 0
    for article in general_news[:2] + business_news[:1] + tech_news[:1]:
        if headlines_added >= 4:
            break

        # Sanitize title and description
        raw_title = article.get("title", "")
        if " - " in raw_title:
            raw_title = raw_title.split(" - ")[0].strip()  # Remove source from title

        title = sanitize_text(raw_title)
        description = sanitize_text(article.get("description", ""))

        if title and description:
            # Create a concise headline with description (limited to 70 chars for safety)
            headline = f"- _{title}: {description[:70]}{'...' if len(description) > 70 else ''}_"

            # Add an appropriate emoji
            if "covid" in title.lower() or "pandemic" in title.lower():
                emoji = " 🦠"
            elif "election" in title.lower() or "vote" in title.lower():
                emoji = " 🗳️"
            elif "economy" in title.lower() or "market" in title.lower():
                emoji = " 📈"
            elif "tech" in title.lower() or "digital" in title.lower():
                emoji = " 💻"
            elif "climate" in title.lower() or "environment" in title.lower():
                emoji = " 🌍"
            else:
                emoji = " 📢"

            summary += f"{headline}{emoji}\n\n"
            headlines_added += 1

    # Add one economic update
    if business_news:
        article = business_news[0]
        # Sanitize title and description
        raw_title = article.get("title", "")
        if " - " in raw_title:
            raw_title = raw_title.split(" - ")[0].strip()

        title = sanitize_text(raw_title)
        description = sanitize_text(article.get("description", ""))

        if title and description:
            economic_update = f"- _{title}: {description[:70]}{'...' if len(description) > 70 else ''}_"
            summary += f"{economic_update} 💰\n\n"

    # Add one international relations update
    # Since we don't have a specific category for this, use general news that mentions international terms
    international_added = False
    international_terms = ["global", "international", "foreign", "diplomat", "treaty", "agreement", "bilateral"]

    for article in general_news + business_news:
        if international_added:
            break

        # Sanitize title and description
        raw_title = article.get("title", "")
        if " - " in raw_title:
            raw_title = raw_title.split(" - ")[0].strip()

        title = sanitize_text(raw_title)
        description = sanitize_text(article.get("description", ""))

        # Check if the article is about international relations
        is_international = any(term in (title + description).lower() for term in international_terms)

        if is_international and title and description:
            international_update = f"- _{title}: {description[:70]}{'...' if len(description) > 70 else ''}_"
            summary += f"{international_update} 🌐\n\n"
            international_added = True

    # If no international news was found, add a generic one
    if not international_added and general_news:
        article = general_news[-1]  # Use the last general news article
        # Sanitize title and description
        raw_title = article.get("title", "")
        if " - " in raw_title:
            raw_title = raw_title.split(" - ")[0].strip()

        title = sanitize_text(raw_title)
        description = sanitize_text(article.get("description", ""))

        if title and description:
            international_update = f"- _{title}: {description[:70]}{'...' if len(description) > 70 else ''}_"
            summary += f"{international_update} 🌐\n\n"

    # Add one sports update
    if sports_news:
        article = sports_news[0]
        # Sanitize title and description
        raw_title = article.get("title", "")
        if " - " in raw_title:
            raw_title = raw_title.split(" - ")[0].strip()

        title = sanitize_text(raw_title)
        description = sanitize_text(article.get("description", ""))

        if title and description:
            sports_update = f"- _{title}: {description[:70]}{'...' if len(description) > 70 else ''}_"

            # Add an appropriate sports emoji
            if "cricket" in (title + description).lower():
                emoji = " 🏏"
            elif "football" in (title + description).lower() or "soccer" in (title + description).lower():
                emoji = " ⚽"
            elif "tennis" in (title + description).lower():
                emoji = " 🎾"
            elif "basketball" in (title + description).lower():
                emoji = " 🏀"
            else:
                emoji = " 🏆"

            summary += f"{sports_update}{emoji}"

    # Ensure the summary is within the 1024 character limit
    if len(summary) > 1024:
        summary = summary[:1021] + "..."

    return summary

def generate_news_summary(country="India"):
    """
    Generate a news summary for a specific country.

    Args:
        country (str): The country to generate news for

    Returns:
        str: The generated news summary
    """
    try:
        # Get the latest news
        articles = get_latest_news(country)

        if not articles or len(articles) < 5:
            logger.warning("Not enough articles found, falling back to Gemini API")
            import gemini_api
            return gemini_api.generate_daily_news_summary(country)

        # Use Gemini API to summarize the news articles
        return summarize_news_with_gemini(articles, country)

    except Exception as e:
        logger.error(f"Error generating news summary: {e}")
        # If all else fails, use Gemini API as a fallback
        import gemini_api
        return gemini_api.generate_daily_news_summary(country)

def summarize_news_with_gemini(articles, country):
    """
    Use Gemini API to summarize news articles.

    Args:
        articles (list): A list of news articles
        country (str): The country the news is for

    Returns:
        str: Formatted news summary
    """
    import gemini_api

    # Get current date in a readable format
    current_date = datetime.now().strftime("%A, %B %d, %Y")

    # Prepare the articles for Gemini
    article_texts = []

    # Categorize articles
    general_news = [a for a in articles if a.get("category") == "general"]
    business_news = [a for a in articles if a.get("category") == "business"]
    sports_news = [a for a in articles if a.get("category") == "sports"]
    tech_news = [a for a in articles if a.get("category") == "technology"]

    # Add general news
    article_texts.append("GENERAL NEWS:")
    for i, article in enumerate(general_news[:5]):
        title = article.get("title", "").split(" - ")[0].strip() if " - " in article.get("title", "") else article.get("title", "")
        description = article.get("description", "")
        article_texts.append(f"{i+1}. {title} - {description}")

    # Add business news
    article_texts.append("\nBUSINESS NEWS:")
    for i, article in enumerate(business_news[:3]):
        title = article.get("title", "").split(" - ")[0].strip() if " - " in article.get("title", "") else article.get("title", "")
        description = article.get("description", "")
        article_texts.append(f"{i+1}. {title} - {description}")

    # Add sports news
    article_texts.append("\nSPORTS NEWS:")
    for i, article in enumerate(sports_news[:3]):
        title = article.get("title", "").split(" - ")[0].strip() if " - " in article.get("title", "") else article.get("title", "")
        description = article.get("description", "")
        article_texts.append(f"{i+1}. {title} - {description}")

    # Add tech news
    article_texts.append("\nTECHNOLOGY NEWS:")
    for i, article in enumerate(tech_news[:3]):
        title = article.get("title", "").split(" - ")[0].strip() if " - " in article.get("title", "") else article.get("title", "")
        description = article.get("description", "")
        article_texts.append(f"{i+1}. {title} - {description}")

    # Create the prompt for Gemini using configurable prompts
    if prompt_config:
        # Get the base prompt from configuration
        base_prompt = prompt_config.get_daily_news_prompt(country)

        # Prepend the article data to the configurable prompt
        prompt = f"""I'm going to provide you with recent news articles from {country} for {current_date}.
Please create a well-formatted daily news summary based on these articles using the format specified below.

Here are the articles:

{"\n".join(article_texts)}

Please follow this format exactly:

{base_prompt}"""
    else:
        # Fallback to hardcoded prompt if prompt_config is not available
        prompt = f"""I'm going to provide you with recent news articles from {country} for {current_date}.
Please create a well-formatted daily news summary based on these articles.

Here are the articles:

{"\n".join(article_texts)}

Format the summary EXACTLY as follows:
- Start with "*DAILY NEWS SUMMARY*" (bold title)
- Each news item should be in italics using underscores: _News item text here._
- Add one blank line between each news item
- Include relevant emojis for each news item
- Focus on the most important and impactful news
- Include a mix of categories: politics, economy, technology, sports, entertainment, etc.

IMPORTANT: The entire summary MUST be between 1000-1024 characters total (including spaces and emojis).
This is a strict requirement as it will be used as an image caption with a 1024 character limit."""

    # Get the summary from Gemini
    summary = gemini_api.generate_content(prompt)

    # Ensure the summary is within the 1024 character limit
    if len(summary) > 1024:
        summary = summary[:1021] + "..."

    return summary
