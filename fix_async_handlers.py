#!/usr/bin/env python3
"""
Script to fix async issues in handler files.
"""

import re
import os

def fix_async_issues(file_path):
    """Fix async issues in a handler file."""
    print(f"Fixing async issues in {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix function signatures - make them async
    content = re.sub(
        r'^def (handle_[^(]+\(update: Update, context: CallbackContext\) -> None:)',
        r'async def \1',
        content,
        flags=re.MULTILINE
    )
    
    # Fix query.answer() calls
    content = re.sub(
        r'(\s+)query\.answer\(\)',
        r'\1await query.answer()',
        content
    )
    
    # Fix query.edit_message_text calls
    content = re.sub(
        r'(\s+)query\.edit_message_text\(',
        r'\1await query.edit_message_text(',
        content
    )
    
    # Fix calls to other async handlers
    async_handlers = [
        'handle_content_settings',
        'handle_project_settings',
        'handle_image_settings',
        'handle_custom_buttons'
    ]
    
    for handler in async_handlers:
        content = re.sub(
            rf'(\s+){handler}\(update, context\)',
            rf'\1await {handler}(update, context)',
            content
        )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed async issues in {file_path}")

def main():
    """Main function to fix async issues in all handler files."""
    handler_files = [
        'handlers/settings_handlers.py',
        'handlers/image_handlers.py', 
        'handlers/button_handlers.py'
    ]
    
    for file_path in handler_files:
        if os.path.exists(file_path):
            fix_async_issues(file_path)
        else:
            print(f"File not found: {file_path}")

if __name__ == "__main__":
    main()
