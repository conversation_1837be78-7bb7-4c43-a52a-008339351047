import os
import random
import requests
import logging
import uuid
import importlib.util

# Check if <PERSON><PERSON> is installed
PILLOW_AVAILABLE = importlib.util.find_spec("PIL") is not None
if PILLOW_AVAILABLE:
    from PIL import Image, ImageDraw, ImageFont

import database as db

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO
)
logger = logging.getLogger(__name__)

# Directory for internet images
INTERNET_IMAGES_DIR = os.path.join(db.DATA_DIR, "internet_images")
os.makedirs(INTERNET_IMAGES_DIR, exist_ok=True)

# Create subdirectories for different content types
NEWS_IMAGES_DIR = os.path.join(INTERNET_IMAGES_DIR, "news")
CRYPTO_IMAGES_DIR = os.path.join(INTERNET_IMAGES_DIR, "crypto")
HEALTH_FITNESS_IMAGES_DIR = os.path.join(INTERNET_IMAGES_DIR, "health_fitness")

# Ensure all directories exist
os.makedirs(INTERNET_IMAGES_DIR, exist_ok=True)
os.makedirs(NEWS_IMAGES_DIR, exist_ok=True)
os.makedirs(CRYPTO_IMAGES_DIR, exist_ok=True)
os.makedirs(HEALTH_FITNESS_IMAGES_DIR, exist_ok=True)

# Create some placeholder images if directories are empty
def create_initial_placeholder_images():
    """Create initial placeholder images if directories are empty."""
    if PILLOW_AVAILABLE:
        try:
            # Check if news directory is empty
            if not os.listdir(NEWS_IMAGES_DIR):
                logger.info("Creating initial placeholder image for news")
                placeholder_path = os.path.join(NEWS_IMAGES_DIR, "placeholder_news.jpg")
                img = Image.new('RGB', (800, 400), color=(73, 109, 137))  # Blue
                d = ImageDraw.Draw(img)
                d.text((400, 200), "Daily News Summary", fill=(255, 255, 255))
                img.save(placeholder_path)

            # Check if crypto directory is empty
            if not os.listdir(CRYPTO_IMAGES_DIR):
                logger.info("Creating initial placeholder image for crypto")
                placeholder_path = os.path.join(CRYPTO_IMAGES_DIR, "placeholder_crypto.jpg")
                img = Image.new('RGB', (800, 400), color=(75, 139, 59))  # Green
                d = ImageDraw.Draw(img)
                d.text((400, 200), "Crypto Prices", fill=(255, 255, 255))
                img.save(placeholder_path)

            # Check if health & fitness directory is empty
            if not os.listdir(HEALTH_FITNESS_IMAGES_DIR):
                logger.info("Creating initial placeholder image for health & fitness")
                placeholder_path = os.path.join(HEALTH_FITNESS_IMAGES_DIR, "placeholder_health_fitness.jpg")
                img = Image.new('RGB', (800, 400), color=(142, 68, 173))  # Purple
                d = ImageDraw.Draw(img)
                d.text((400, 200), "Health & Fitness", fill=(255, 255, 255))
                img.save(placeholder_path)
        except Exception as e:
            logger.error(f"Error creating initial placeholder images: {e}")

# Create initial placeholder images
create_initial_placeholder_images()

# Maximum number of images to store per category
MAX_IMAGES_PER_CATEGORY = 10

# List of reliable image URLs that we know work
RELIABLE_NEWS_IMAGES = [
    "https://images.pexels.com/photos/518543/pexels-photo-518543.jpeg",  # Newspaper
    "https://images.pexels.com/photos/3944454/pexels-photo-3944454.jpeg",  # News
    "https://images.pexels.com/photos/3856050/pexels-photo-3856050.jpeg",  # Newspaper
    "https://images.pexels.com/photos/6335/man-coffee-cup-pen.jpg",  # Reading news
    "https://images.pexels.com/photos/4057663/pexels-photo-4057663.jpeg",  # World news
    "https://images.pexels.com/photos/261949/pexels-photo-261949.jpeg",  # Newspaper
    "https://images.pexels.com/photos/159652/table-newspaper-news-read-159652.jpeg",  # Newspaper
    "https://images.pexels.com/photos/1591056/pexels-photo-1591056.jpeg",  # News
    "https://images.pexels.com/photos/1550337/pexels-photo-1550337.jpeg",  # Reading news
    "https://images.pexels.com/photos/4245826/pexels-photo-4245826.jpeg"   # World news
]

RELIABLE_CRYPTO_IMAGES = [
    "https://images.pexels.com/photos/844124/pexels-photo-844124.jpeg",  # Bitcoin
    "https://images.pexels.com/photos/6780789/pexels-photo-6780789.jpeg",  # Crypto
    "https://images.pexels.com/photos/6771900/pexels-photo-6771900.jpeg",  # Crypto mining
    "https://images.pexels.com/photos/8370752/pexels-photo-8370752.jpeg",  # Crypto chart
    "https://images.pexels.com/photos/8358089/pexels-photo-8358089.jpeg",  # Bitcoin
    "https://images.pexels.com/photos/7788009/pexels-photo-7788009.jpeg",  # Crypto
    "https://images.pexels.com/photos/6772076/pexels-photo-6772076.jpeg",  # Bitcoin
    "https://images.pexels.com/photos/6772084/pexels-photo-6772084.jpeg",  # Crypto mining
    "https://images.pexels.com/photos/6771985/pexels-photo-6771985.jpeg",  # Crypto chart
    "https://images.pexels.com/photos/8370784/pexels-photo-8370784.jpeg"   # Bitcoin
]

RELIABLE_HEALTH_FITNESS_IMAGES = [
    "https://images.pexels.com/photos/2294361/pexels-photo-2294361.jpeg",  # Fitness
    "https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg",  # Healthy food
    "https://images.pexels.com/photos/3490348/pexels-photo-3490348.jpeg",  # Workout
    "https://images.pexels.com/photos/1153369/pexels-photo-1153369.jpeg",  # Yoga
    "https://images.pexels.com/photos/3822906/pexels-photo-3822906.jpeg",  # Nutrition
    "https://images.pexels.com/photos/1346086/pexels-photo-1346086.jpeg",  # Healthy lifestyle
    "https://images.pexels.com/photos/3094230/pexels-photo-3094230.jpeg",  # Fitness training
    "https://images.pexels.com/photos/775032/pexels-photo-775032.jpeg",    # Meditation
    "https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg",  # Healthy breakfast
    "https://images.pexels.com/photos/3076509/pexels-photo-3076509.jpeg"   # Exercise
]

# Keep track of the last used image URL to avoid repeating
LAST_USED_IMAGE_URL = {"daily_news_summary": None, "crypto_prices": None, "health_fitness": None}

def search_and_download_image(query=None, content_type=None):
    """
    Search for an image on the internet and download it.

    Args:
        query (str): The search query
        content_type (str): The content type (e.g., 'daily_news_summary', 'crypto_prices')

    Returns:
        str: Path to the downloaded image, or None if failed
    """
    try:
        # Determine the appropriate directory and image list based on content type
        if content_type == "daily_news_summary":
            save_dir = NEWS_IMAGES_DIR
            reliable_images = RELIABLE_NEWS_IMAGES
        elif content_type == "crypto_prices":
            save_dir = CRYPTO_IMAGES_DIR
            reliable_images = RELIABLE_CRYPTO_IMAGES
        elif content_type == "health_fitness":
            save_dir = HEALTH_FITNESS_IMAGES_DIR
            reliable_images = RELIABLE_HEALTH_FITNESS_IMAGES
        else:
            save_dir = INTERNET_IMAGES_DIR
            # Combine all lists for other content types
            reliable_images = RELIABLE_NEWS_IMAGES + RELIABLE_CRYPTO_IMAGES + RELIABLE_HEALTH_FITNESS_IMAGES

        # Choose a random image from our reliable list, avoiding the last used one
        global LAST_USED_IMAGE_URL

        # Filter out the last used image URL if there are multiple options
        available_images = [img for img in reliable_images if img != LAST_USED_IMAGE_URL.get(content_type)]

        # If we've filtered out all images (unlikely), use the full list
        if not available_images and len(reliable_images) > 0:
            available_images = reliable_images

        # Choose a random image from the available ones
        if available_images:
            image_url = random.choice(available_images)
            # Update the last used image URL
            LAST_USED_IMAGE_URL[content_type] = image_url
            logger.info(f"Selected image URL: {image_url} (avoiding last used: {LAST_USED_IMAGE_URL.get(content_type)})")
        else:
            # Fallback if no images are available (shouldn't happen)
            image_url = None
            logger.error("No image URLs available")

        # Try to download the image
        try:
            logger.info(f"Downloading image from: {image_url}")
            response = requests.get(image_url, timeout=15)  # Increased timeout

            if response.status_code == 200:
                # Generate a unique filename
                filename = f"{uuid.uuid4()}.jpg"
                filepath = os.path.join(save_dir, filename)

                # Save the image
                try:
                    with open(filepath, 'wb') as f:
                        f.write(response.content)

                    logger.info(f"Downloaded image to {filepath}")

                    # Verify the image was saved correctly
                    if os.path.exists(filepath) and os.path.getsize(filepath) > 0:
                        # Clean up old images if we have too many
                        cleanup_old_images(save_dir)
                        return filepath
                    else:
                        logger.error(f"Image file is empty or doesn't exist: {filepath}")
                except Exception as e:
                    logger.error(f"Error saving image from {image_url}: {e}")
            else:
                logger.warning(f"Failed to fetch image from {image_url}: {response.status_code}")
        except Exception as e:
            logger.warning(f"Error fetching from {image_url}: {e}")

        # If we get here, the reliable image failed, create a placeholder
        logger.error("Failed to download image, creating placeholder")
        return create_placeholder_image(content_type, save_dir)

    except Exception as e:
        logger.error(f"Error searching for image: {e}")
        return create_placeholder_image(content_type, save_dir)

def create_placeholder_image(content_type, save_dir):
    """Create a placeholder image when download fails."""
    try:
        if PILLOW_AVAILABLE:
            logger.info("Creating placeholder image")
            placeholder_path = os.path.join(save_dir, f"placeholder_{uuid.uuid4()}.jpg")

            # Create different placeholder images based on content type
            if content_type == "daily_news_summary":
                bg_color = (73, 109, 137)  # Blue
                text = "Daily News Summary"
            elif content_type == "crypto_prices":
                bg_color = (75, 139, 59)  # Green
                text = "Crypto Prices"
            elif content_type == "health_fitness":
                bg_color = (142, 68, 173)  # Purple
                text = "Health & Fitness"
            else:
                bg_color = (128, 128, 128)  # Gray
                text = "Content"

            img = Image.new('RGB', (800, 400), color=bg_color)
            d = ImageDraw.Draw(img)
            d.text((400, 200), text, fill=(255, 255, 255))

            img.save(placeholder_path)
            logger.info(f"Created placeholder image at {placeholder_path}")
            return placeholder_path
    except Exception as e:
        logger.error(f"Error creating placeholder image: {e}")

    # If all else fails, return None
    return None

def get_random_image_for_content_type(content_type, force_download=True):
    """
    Get a random image for the specified content type.
    If no images are available, download a new one.

    Args:
        content_type (str): The content type (e.g., 'daily_news_summary', 'crypto_prices', 'health_fitness')
        force_download (bool): If True, always download a new image

    Returns:
        str: Path to an image, or None if failed
    """
    # Determine the appropriate directory based on content type
    if content_type == "daily_news_summary":
        image_dir = NEWS_IMAGES_DIR
    elif content_type == "crypto_prices":
        image_dir = CRYPTO_IMAGES_DIR
    elif content_type == "health_fitness":
        image_dir = HEALTH_FITNESS_IMAGES_DIR
    else:
        image_dir = INTERNET_IMAGES_DIR

    # If force_download is True, always get a new image
    if force_download:
        logger.info(f"Forcing download of new image for {content_type}")

        # Clear any existing non-placeholder images to ensure we get a fresh one
        try:
            images = [os.path.join(image_dir, f) for f in os.listdir(image_dir)
                    if os.path.isfile(os.path.join(image_dir, f)) and
                    f.lower().endswith(('.png', '.jpg', '.jpeg')) and
                    not f.startswith('placeholder_')]

            # Remove all non-placeholder images to force a fresh download
            for img in images:
                try:
                    os.remove(img)
                    logger.info(f"Removed existing image to force fresh download: {img}")
                except Exception as e:
                    logger.error(f"Error removing image {img}: {e}")
        except Exception as e:
            logger.error(f"Error clearing image cache: {e}")

        # Download a new image
        new_image = search_and_download_image(None, content_type)
        if new_image:
            return new_image

    # Otherwise, check if we have any cached images
    try:
        images = [os.path.join(image_dir, f) for f in os.listdir(image_dir)
                if os.path.isfile(os.path.join(image_dir, f)) and
                f.lower().endswith(('.png', '.jpg', '.jpeg')) and
                not f.startswith('placeholder_')]  # Skip placeholder images

        # If we have images, return a random one
        if images:
            selected_image = random.choice(images)
            logger.info(f"Using existing image from cache: {selected_image}")
            return selected_image
    except Exception as e:
        logger.error(f"Error getting existing images: {e}")

    # If we don't have images or there was an error, download a new one
    logger.info(f"No cached images found for {content_type}, downloading new image")
    return search_and_download_image(None, content_type)  # Pass None for query, we'll use our predefined lists

def cleanup_old_images(directory):
    """
    Remove old images if we have too many in a directory.

    Args:
        directory (str): The directory to clean up
    """
    try:
        # Get all image files in the directory
        images = [os.path.join(directory, f) for f in os.listdir(directory)
                if os.path.isfile(os.path.join(directory, f)) and
                f.lower().endswith(('.png', '.jpg', '.jpeg'))]

        # First, remove any placeholder images if we have regular images
        regular_images = [img for img in images if not os.path.basename(img).startswith('placeholder_')]
        placeholder_images = [img for img in images if os.path.basename(img).startswith('placeholder_')]

        # If we have regular images, remove all placeholder images
        if regular_images and placeholder_images:
            for img in placeholder_images:
                try:
                    os.remove(img)
                    logger.info(f"Removed placeholder image: {img}")
                except Exception as e:
                    logger.error(f"Error removing placeholder image {img}: {e}")

        # Get updated list of images after removing placeholders
        images = [os.path.join(directory, f) for f in os.listdir(directory)
                if os.path.isfile(os.path.join(directory, f)) and
                f.lower().endswith(('.png', '.jpg', '.jpeg'))]

        # If we have too many images, remove the oldest ones
        if len(images) > MAX_IMAGES_PER_CATEGORY:
            # Sort by modification time (oldest first)
            images.sort(key=lambda x: os.path.getmtime(x))

            # Remove the oldest images
            for img in images[:len(images) - MAX_IMAGES_PER_CATEGORY]:
                try:
                    os.remove(img)
                    logger.info(f"Removed old image: {img}")
                except Exception as e:
                    logger.error(f"Error removing old image {img}: {e}")
    except Exception as e:
        logger.error(f"Error cleaning up old images: {e}")
