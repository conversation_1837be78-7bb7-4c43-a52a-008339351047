import os
import json
import requests
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API key from environment variables
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"

def generate_content(prompt):
    """
    Generate content using the Gemini 2.0 Flash API.

    Args:
        prompt (str): The prompt to send to the Gemini API

    Returns:
        str: The generated content or error message
    """
    if not GEMINI_API_KEY:
        return "Error: Gemini API key not found. Please set the GEMINI_API_KEY environment variable."

    url = f"{GEMINI_API_URL}?key={GEMINI_API_KEY}"

    headers = {
        "Content-Type": "application/json"
    }

    data = {
        "contents": [{
            "parts": [{"text": prompt}]
        }]
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # Raise an exception for HTTP errors

        result = response.json()

        # Extract the generated text from the response
        if "candidates" in result and len(result["candidates"]) > 0:
            if "content" in result["candidates"][0] and "parts" in result["candidates"][0]["content"]:
                parts = result["candidates"][0]["content"]["parts"]
                if parts and "text" in parts[0]:
                    return parts[0]["text"]

        return "Error: Unable to extract content from API response."

    except requests.exceptions.RequestException as e:
        return f"Error: API request failed - {str(e)}"
    except json.JSONDecodeError:
        return "Error: Invalid JSON response from API"
    except Exception as e:
        return f"Error: {str(e)}"

def generate_daily_news_summary(country="India"):
    """
    Generate a daily news summary for a specific country.

    Args:
        country (str): The country to generate news for (default: India)

    Returns:
        str: The generated news summary
    """
    try:
        # Try to use the news API first
        import news_api
        return news_api.generate_news_summary(country)
    except Exception as e:
        # If the news API fails, fall back to Gemini
        print(f"News API failed: {e}. Falling back to Gemini.")

        # Get current date in a readable format
        current_date = datetime.now().strftime("%A, %B %d, %Y")

        prompt = f"""
        Generate a daily news summary for {country} for TODAY, {current_date}.

        IMPORTANT: You MUST include ONLY THE MOST RECENT NEWS from the past 24 hours. Do NOT include old news from previous days or weeks.
        Use your knowledge cutoff to determine what is current and what is not. If you're unsure if something is current, do not include it.

        Please include:
        1. 3-4 major headlines with brief descriptions from TODAY or YESTERDAY only
        2. One important economic update from the LAST 24-48 HOURS
        3. One notable international relations update involving {country} from the LAST 24-48 HOURS
        4. One major sports event or result from the LAST 24-48 HOURS

        Format requirements:
        - Start with "📰 *DAILY NEWS SUMMARY: {country} - {current_date}*" as the title (make the entire title bold using asterisks).
        - Format each news item as a bullet point using a dash (-) for bullets.
        - DO NOT include category labels like "Headline:" or "Economy:" at the beginning of each line.
        - Make all news content lines _italic_ using underscores, not asterisks.
        - Include relevant emojis at the end of each line to make the content engaging.
        - Add a blank line between each news item.
        - Make full use of the available space - aim for 1000-1020 characters total.

        Example format:
        📰 *DAILY NEWS SUMMARY: India - Thursday, April 17, 2025*

        - _Monsoon Arrives Early in Kerala: Heavy rainfall brings relief after prolonged heatwave._ 🌧️

        - _Lok Sabha Elections: Phase 2 sees high voter turnout across key states._ 🗳️🇮🇳

        - _ISRO Launches Advanced Earth Observation Satellite: Enhancing disaster management capabilities._ 🛰️

        - _Delhi Government Announces New Electric Vehicle Subsidy Scheme: Aiming to boost adoption._ 🚗⚡️

        - _India's GDP growth projected at 7.2% by World Bank, citing strong manufacturing output._ 📈💰

        - _India and Japan collaborate on developing next-gen hydrogen fuel technology for automobiles._ 🤝🇯🇵🇮🇳

        - _PV Sindhu wins Badminton Asia Championships, defeating China's Chen Yufei in a thrilling final!_ 🏸🏆🎉

        IMPORTANT: The entire summary MUST be between 1000-1024 characters total (including spaces and emojis).
        This is a strict requirement as it will be used as an image caption with a 1024 character limit.

        CRITICAL: All news items MUST be from the LAST 24-48 HOURS ONLY. Do NOT include old news.
        """

        content = generate_content(prompt)

        # Ensure the content is within the 1024 character limit
        if len(content) > 1024:
            # Truncate if too long
            content = content[:1021] + "..."

        return content

def generate_crypto_prices():
    """
    Generate a daily cryptocurrency price update.

    Returns:
        str: The generated crypto price update
    """
    # Get current date in a readable format
    current_date = datetime.now().strftime("%A, %B %d, %Y")

    prompt = f"""
    Generate a daily cryptocurrency price update for {current_date}.

    IMPORTANT: You MUST provide the MOST CURRENT cryptocurrency information you have access to.
    If you're unsure about current prices, focus on the trends and general market conditions instead.

    Please include:
    1. Current prices and 24-hour changes for Bitcoin, Ethereum, and 2 other major cryptocurrencies
    2. Brief overview of the current crypto market sentiment (bullish/bearish/neutral)
    3. One or two trending cryptocurrencies that have seen significant movement
    4. Top gainers and losers in the past 24 hours

    Format requirements:
    - Start with "💰 *DAILY CRYPTO UPDATE: {current_date}*" as the title (make the entire title bold using asterisks).
    - Format each crypto item as a bullet point using a dash (-) for bullets.
    - Make all content lines _italic_ using underscores, not asterisks.
    - Include relevant emojis at the end of each line to make the content engaging.
    - Add a blank line between each section.
    - Make full use of the available space - aim for 1000-1020 characters total.

    Example format:
    💰 *DAILY CRYPTO UPDATE: Thursday, April 17, 2025*

    - _Bitcoin (BTC): $72,450 | +2.3% in 24h | Market dominance remains strong amid institutional buying._ 📈

    - _Ethereum (ETH): $3,890 | -0.7% in 24h | Network activity increasing ahead of upcoming protocol upgrade._ 🔧

    - _Solana (SOL): $142 | +5.2% in 24h | DeFi applications on Solana see surge in new users._ 🚀

    - _Cardano (ADA): $0.58 | -1.3% in 24h | Development activity remains high despite price decline._ 📉

    - _Market Sentiment: Cautiously bullish as Bitcoin holds above key support levels despite global economic uncertainty._ 📈🐻

    - _Trending: Arbitrum (ARB) gaining attention with 17% surge after major partnership announcement._ 💥

    - _Top Gainer: Render (RNDR) +23% after integration with major AI platform._ 🌟

    - _Top Loser: Filecoin (FIL) -12% following network storage metrics below expectations._ 📉

    IMPORTANT: The entire update MUST be between 1000-1024 characters total (including spaces and emojis).
    This is a strict requirement as it will be used as an image caption with a 1024 character limit.

    CRITICAL: Focus on providing the MOST CURRENT information available to you.
    """

    content = generate_content(prompt)

    # Ensure the content is within the 1024 character limit
    if len(content) > 1024:
        # Truncate if too long
        content = content[:1021] + "..."

    return content

def generate_health_fitness_content():
    """
    Generate health, nutrition, and fitness content.

    Returns:
        str: The generated health and fitness content
    """
    # Get current date in a readable format
    current_date = datetime.now().strftime("%A, %B %d, %Y")

    prompt = f"""
    Generate valuable health, nutrition, and fitness content for {current_date}.

    IMPORTANT: Create a well-structured, informative post that provides genuinely useful health and fitness advice.
    Focus on creating content that is readable, valuable, and presents new information each time.

    Please include:
    1. A main topic or theme for today's health & fitness post (choose something specific and actionable)
    2. 3-4 practical, evidence-based tips or insights related to the main topic
    3. One nutrition recommendation or fact that complements the main topic
    4. One exercise or physical activity suggestion that readers can try immediately
    5. A brief motivational closing note that inspires action

    Format requirements:
    - Start with a catchy, emoji-rich headline that clearly states the topic (use bold with asterisks)
    - Use a visually appealing format with clear sections and spacing
    - Use emojis strategically to highlight key points and make the content visually engaging
    - Use a mix of formatting: bold for section headers, italic for important points
    - Make the content scannable with numbered tips or clear bullet points
    - Include a "Try This Today" section with one immediate action item
    - End with a motivational quote or call to action
    - Make full use of the available space - aim for 1000-1020 characters total

    Example format (but feel free to improve upon this):
    *🔥 BOOST YOUR METABOLISM: 5 SCIENCE-BACKED STRATEGIES 🔥*

    Want to optimize your body's calorie-burning potential? Here's how to naturally rev up your metabolism:

    *1️⃣ Strength Training*
    Build lean muscle through compound exercises like squats and deadlifts. _Each pound of muscle burns 6 calories daily at rest!_ 💪

    *2️⃣ Protein-Rich Meals*
    Your body burns 20-30% of protein calories during digestion vs. only 5-10% for carbs. _Aim for 25-30g protein per meal._ 🥩🥚

    *3️⃣ HIIT Workouts*
    Just 20 minutes of high-intensity interval training can boost metabolism for up to 24 hours. _The afterburn effect is real!_ 🔄

    *4️⃣ Stay Hydrated*
    Drinking 500ml of water increases metabolic rate by 30% for up to 40 minutes. _Cold water works best!_ 💧❄️

    *🍎 NUTRITION TIP:*
    Spicy foods containing capsaicin can temporarily boost metabolism by up to 8%. Add cayenne or hot peppers to your meals! 🌶️

    *💯 TRY THIS TODAY:*
    Do a 4-minute Tabata: 20 seconds of burpees, 10 seconds rest. Repeat 8 times. Burns calories for hours afterward!

    "The only bad workout is the one that didn't happen. Start where you are, use what you have, do what you can." 🌟

    IMPORTANT: The entire post MUST be between 1000-1024 characters total (including spaces and emojis).
    This is a strict requirement as it will be used as an image caption with a 1024 character limit.

    CRITICAL: Create UNIQUE content each time with NEW information and topics. Do not repeat the same tips or information.
    """

    content = generate_content(prompt)

    # Ensure the content is within the 1024 character limit
    if len(content) > 1024:
        # Truncate if too long
        content = content[:1021] + "..."

    return content
