"""
<PERSON><PERSON><PERSON> to create a default cricket news image for the bot.
"""
import os
from PIL import Image, ImageDraw, ImageFont
import database as db

def create_cricket_news_image():
    """Create a default cricket news image."""
    # Ensure the images directory exists
    os.makedirs(db.IMAGE_DIR, exist_ok=True)
    
    # Create image
    width, height = 800, 400
    bg_color = (34, 139, 34)  # Forest Green
    
    # Create image
    img = Image.new('RGB', (width, height), color=bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a nice font, fall back to default if not available
    try:
        font_large = ImageFont.truetype("arial.ttf", 48)
        font_small = ImageFont.truetype("arial.ttf", 24)
    except:
        try:
            font_large = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 48)
            font_small = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
        except:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
    
    # Add cricket emoji and text
    title_text = "🏏 CRICKET NEWS"
    subtitle_text = "AND UPDATES"
    
    # Calculate text positions
    title_bbox = draw.textbbox((0, 0), title_text, font=font_large)
    title_width = title_bbox[2] - title_bbox[0]
    title_height = title_bbox[3] - title_bbox[1]
    
    subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=font_large)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_height = subtitle_bbox[3] - subtitle_bbox[1]
    
    # Center the text
    title_x = (width - title_width) // 2
    title_y = (height - title_height - subtitle_height - 20) // 2
    
    subtitle_x = (width - subtitle_width) // 2
    subtitle_y = title_y + title_height + 20
    
    # Draw text with shadow effect
    shadow_offset = 3
    shadow_color = (0, 0, 0, 128)
    text_color = (255, 255, 255)
    
    # Draw shadow
    draw.text((title_x + shadow_offset, title_y + shadow_offset), title_text, font=font_large, fill=shadow_color)
    draw.text((subtitle_x + shadow_offset, subtitle_y + shadow_offset), subtitle_text, font=font_large, fill=shadow_color)
    
    # Draw main text
    draw.text((title_x, title_y), title_text, font=font_large, fill=text_color)
    draw.text((subtitle_x, subtitle_y), subtitle_text, font=font_large, fill=text_color)
    
    # Add decorative elements
    # Draw cricket ball
    ball_radius = 30
    ball_x = 100
    ball_y = height - 100
    
    # Cricket ball (red with white seam)
    draw.ellipse([ball_x - ball_radius, ball_y - ball_radius, 
                  ball_x + ball_radius, ball_y + ball_radius], 
                 fill=(220, 20, 60))  # Crimson
    
    # Cricket ball seam
    draw.line([ball_x - ball_radius + 5, ball_y, ball_x + ball_radius - 5, ball_y], 
              fill=(255, 255, 255), width=3)
    draw.arc([ball_x - ball_radius + 5, ball_y - 15, ball_x + ball_radius - 5, ball_y + 15], 
             start=0, end=180, fill=(255, 255, 255), width=2)
    draw.arc([ball_x - ball_radius + 5, ball_y - 15, ball_x + ball_radius - 5, ball_y + 15], 
             start=180, end=360, fill=(255, 255, 255), width=2)
    
    # Draw cricket bat
    bat_x = width - 120
    bat_y = height - 150
    bat_width = 15
    bat_height = 100
    
    # Bat blade
    draw.rectangle([bat_x, bat_y, bat_x + bat_width, bat_y + bat_height], 
                   fill=(222, 184, 135))  # Burlywood
    
    # Bat handle
    handle_height = 30
    draw.rectangle([bat_x + 3, bat_y - handle_height, bat_x + bat_width - 3, bat_y], 
                   fill=(139, 69, 19))  # Saddle brown
    
    # Add border
    border_width = 5
    draw.rectangle([border_width, border_width, 
                    width - border_width, height - border_width], 
                   outline=(255, 255, 255), width=border_width)
    
    # Save the image
    image_path = os.path.join(db.IMAGE_DIR, 'cricketNews.png')
    img.save(image_path, 'PNG')
    print(f"Cricket news image created: {image_path}")
    
    return image_path

if __name__ == "__main__":
    create_cricket_news_image()
