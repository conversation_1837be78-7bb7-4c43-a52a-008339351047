"""
Telegram Auto-Posting Bot - Main Entry Point
"""
import os
import logging
from dotenv import load_dotenv
from telegram.ext import (
    <PERSON>date<PERSON>,
    CommandHandler,
    CallbackQueryHandler,
)

# Import database and content poster modules
import database as db
import content_poster as poster

# Import handlers
from handlers.command_handlers import (
    start,
    help_command,
    cancel,
    show_server_time,
    check_posting_status,
    reset_last_posted_command,
    handle_command_buttons,
)
from handlers.channel_handlers import list_channels
from handlers.project_handlers import (
    list_projects,
    handle_project_settings,
    handle_toggle_project,
    handle_delete_project,
    handle_confirm_delete,
    handle_back_to_projects,
    handle_test_post,
    manual_post_command,
    handle_manual_post,
)
from handlers.settings_handlers import (
    handle_content_settings,
    handle_configure_content,
    handle_show_countries,
    handle_set_country,
    handle_set_num_coins,
    handle_set_post_time,
    handle_save_post_time,
    handle_set_timezone,
    handle_save_timezone,
)
from handlers.button_handlers import (
    handle_custom_buttons,
    handle_toggle_buttons,
    handle_remove_button,
    handle_confirm_remove_button,
)
from handlers.image_handlers import (
    handle_image_settings,
    handle_set_image_mode,
    handle_reset_image,
)
from handlers.time_handlers import (
    handle_set_frequency,
    handle_save_frequency,
    handle_reset_last_posted,
)
from handlers.conversation_handlers import (
    get_add_channel_conversation_handler,
    get_add_project_conversation_handler,
    get_custom_time_conversation_handler,
    get_add_button_conversation_handler,
    get_upload_image_conversation_handler,
)

# Load environment variables
load_dotenv()

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO
)
logger = logging.getLogger(__name__)

def start(update: Update, context: CallbackContext) -> None:
    """Send a message when the command /start is issued."""
    user = update.effective_user
    try:
        update.message.reply_text(
            f"👋 *Welcome, {user.first_name}!*\n\n"
            f"📰 I'm your Telegram Auto-Posting Bot that creates daily news summaries for your channels.\n\n"
            f"🔍 *Getting Started:*\n"
            f"1️⃣ First, use /addchannel to add your Telegram channel\n"
            f"2️⃣ Then, use /addprojects to create a new auto-posting project\n"
            f"3️⃣ Configure your project settings and enjoy automated posting!\n\n"
            f"📋 *Available Commands:*\n"
            f"• /addchannel - Add a new channel (forward a message from it)\n"
            f"• /channels - View your added channels\n"
            f"• /addprojects - Create a new daily news summary project\n"
            f"• /projects - Manage your existing projects\n"
            f"• /post - Manually trigger a post for a project\n"
            f"• /resetlast - Reset the last posted time for a project\n"
            f"• /help - Show detailed help information\n"
            f"• /cancel - Cancel current operation\n\n"
            f"🚀 Let's get started!",
            parse_mode="Markdown"
        )
    except Exception as e:
        logging.error(f"Error in start command: {str(e)}")
        # Fallback to plain text if there's an error with Markdown parsing
        update.message.reply_text(
            f"👋 Welcome, {user.first_name}!\n\n"
            f"📰 I'm your Telegram Auto-Posting Bot that creates daily news summaries for your channels.\n\n"
            f"🔍 Getting Started:\n"
            f"1. First, use /addchannel to add your Telegram channel\n"
            f"2. Then, use /addprojects to create a new auto-posting project\n"
            f"3. Configure your project settings and enjoy automated posting!\n\n"
            f"📋 Available Commands:\n"
            f"• /addchannel - Add a new channel (forward a message from it)\n"
            f"• /channels - View your added channels\n"
            f"• /addprojects - Create a new daily news summary project\n"
            f"• /projects - Manage your existing projects\n"
            f"• /post - Manually trigger a post for a project\n"
            f"• /resetlast - Reset the last posted time for a project\n"
            f"• /help - Show detailed help information\n"
            f"• /cancel - Cancel current operation\n\n"
            f"🚀 Let's get started!"
        )


def help_command(update: Update, context: CallbackContext) -> None:
    """Send a detailed help message when the command /help is issued."""
    try:
        update.message.reply_text(
            f"📖 *Detailed Help Guide*\n\n"

            f"📱 *Channel Management:*\n"
            f"• /addchannel - Add a new Telegram channel to the bot\n"
            f"  The bot needs to be an admin in your channel\n"
            f"  Forward any message from your channel to the bot\n\n"
            f"• /channels - List all your added channels\n"
            f"  View all channels where you can create auto-posting projects\n\n"

            f"📅 *Project Management:*\n"
            f"• /addprojects - Create a new auto-posting project\n"
            f"  Select a channel and configure posting settings\n"
            f"  Currently supports Daily News Summary content type\n\n"
            f"• /projects - Manage your existing projects\n"
            f"  Toggle projects on/off, change settings, test posts\n"
            f"  Configure posting frequency, custom images, and buttons\n\n"

            f"📰 *Content Features:*\n"
            f"• Daily News Summary: Automatic news collection and summarization\n"
            f"• Crypto Prices: Daily cryptocurrency price updates\n"
            f"• Customizable posting schedule (time and frequency)\n"
            f"• Custom images for each project\n"
            f"• Custom buttons that can be added below posts\n"
            f"• Post history tracking\n\n"

            f"⏰ *Time Settings:*\n"
            f"• Each project can have its own time zone setting\n"
            f"• Posts will be scheduled according to the selected country's time zone\n"
            f"• Use /time to see current times in all supported countries\n\n"

            f"🔄 *Other Commands:*\n"
            f"• /start - Show the welcome message\n"
            f"• /help - Show this help message\n"
            f"• /time - Show the current server time\n"
            f"• /post - Manually trigger a post for a project\n"
            f"• /status - Check the status of posting threads\n"
            f"• /resetlast - Reset the last posted time for a project\n"
            f"• /cancel - Cancel the current operation\n\n"

            f"❓ *Need more help?*\n"
            f"If you have any questions or issues, please contact the bot administrator.",
            parse_mode="Markdown"
        )
    except Exception as e:
        logging.error(f"Error in help_command: {str(e)}")
        # Fallback to plain text if there's an error with Markdown parsing
        update.message.reply_text(
            f"📖 Detailed Help Guide\n\n"

            f"📱 Channel Management:\n"
            f"• /addchannel - Add a new Telegram channel to the bot\n"
            f"  The bot needs to be an admin in your channel\n"
            f"  Forward any message from your channel to the bot\n\n"
            f"• /channels - List all your added channels\n"
            f"  View all channels where you can create auto-posting projects\n\n"

            f"📅 Project Management:\n"
            f"• /addprojects - Create a new auto-posting project\n"
            f"  Select a channel and configure posting settings\n"
            f"  Currently supports Daily News Summary content type\n\n"
            f"• /projects - Manage your existing projects\n"
            f"  Toggle projects on/off, change settings, test posts\n"
            f"  Configure posting frequency, custom images, and buttons\n\n"

            f"📰 Daily News Summary Features:\n"
            f"• Automatic news collection and summarization\n"
            f"• Customizable posting schedule (time and frequency)\n"
            f"• Custom images for each project\n"
            f"• Custom buttons that can be added below posts\n"
            f"• Post history tracking\n\n"

            f"🔄 Other Commands:\n"
            f"• /start - Show the welcome message\n"
            f"• /help - Show this help message\n"
            f"• /cancel - Cancel the current operation\n\n"

            f"❓ Need more help?\n"
            f"If you have any questions or issues, please contact the bot administrator."
        )


def add_channel(update: Update, context: CallbackContext) -> int:
    """Start the process of adding a channel."""
    # Always use plain text for this message to avoid Markdown parsing issues
    bot_username = context.bot.username if context.bot and context.bot.username else "the bot"

    update.message.reply_text(
        "📲 Adding a Channel\n\n"
        "To add your channel, I need to know its ID. Please follow these steps:\n\n"
        "1. Make sure the bot (@" + bot_username + ") is added as an admin to your channel\n"
        "2. Forward ANY message from your channel to me\n"
        "3. I'll automatically detect the channel details\n\n"
        "⚠️ Important: The bot must be an admin in your channel with permission to post messages.",
        reply_markup=ForceReply(selective=True)
    )
    return AWAITING_CHANNEL_FORWARD

def check_bot_permissions(bot, channel_id):
    """Check if the bot has necessary permissions in the channel."""
    try:
        # Try to get the bot's member info in the channel
        bot_member = bot.get_chat_member(chat_id=channel_id, user_id=bot.id)

        # Check if the bot is an admin
        if bot_member.status in ['administrator', 'creator']:
            # Check if the bot has posting permissions
            if hasattr(bot_member, 'can_post_messages') and bot_member.can_post_messages:
                return True, "Bot has all required permissions."
            else:
                return False, "Bot is an admin but doesn't have permission to post messages."
        else:
            return False, f"Bot is not an admin in this channel (status: {bot_member.status})."

    except Exception as e:
        return False, f"Error checking permissions: {str(e)}"

def handle_forwarded_message(update: Update, context: CallbackContext) -> int:
    """Handle the forwarded message to add a channel."""
    try:
        message = update.message

        # Check if this is a command (user might be trying to cancel)
        if message.text and message.text.startswith('/'):
            if message.text.startswith('/cancel'):
                update.message.reply_text(
                    "\ud83d\uded1 Channel addition cancelled.\n\n"
                    "You can use /addchannel again when you're ready to add a channel."
                )
                return ConversationHandler.END
            else:
                # If it's another command, remind them we're waiting for a forwarded message
                update.message.reply_text(
                    "\ud83d\udcf2 I'm still waiting for you to forward a message from your channel.\n\n"
                    "Please forward any message from the channel you want to add, or\n"
                    "use /cancel to cancel the operation."
                )
                return AWAITING_CHANNEL_FORWARD

        # Check if the message is forwarded from a channel
        if message.forward_from_chat and message.forward_from_chat.type in ['channel', 'supergroup']:
            channel = message.forward_from_chat
            channel_id = str(channel.id)
            channel_name = channel.title
            channel_type = channel.type

            # Check if the bot has necessary permissions
            has_permissions, permission_message = check_bot_permissions(context.bot, channel_id)

            if not has_permissions:
                update.message.reply_text(
                    f"\u26a0\ufe0f Cannot add channel '{channel_name}':\n"
                    f"{permission_message}\n\n"
                    f"Please make sure to:\n"
                    f"1. Add the bot as an admin to the channel\n"
                    f"2. Give the bot 'Post Messages' permission\n"
                    f"3. Try adding the channel again\n\n"
                    f"Use /cancel if you want to cancel adding a channel."
                )
                return AWAITING_CHANNEL_FORWARD

            # Add the channel to the database
            success = db.add_channel(channel_id, channel_name, channel_type)

            if success:
                update.message.reply_text(
                    f"\u2705 Channel '{channel_name}' has been added successfully!\n\n"
                    f"You can now use it in your auto-posting projects."
                )
            else:
                update.message.reply_text(
                    f"\u26a0\ufe0f Channel '{channel_name}' is already in your list."
                )

            return ConversationHandler.END
        else:
            # This is a regular message, not forwarded from a channel
            keyboard = [[InlineKeyboardButton("Cancel", callback_data="cancel_add_channel")]]
            update.message.reply_text(
                "\u26a0\ufe0f This is not a forwarded message from a channel.\n\n"
                "To add a channel, please:\n"
                "1. Go to your channel\n"
                "2. Select any message\n"
                "3. Click 'Forward'\n"
                "4. Select this chat and send the forwarded message to me\n\n"
                "Or click the button below to cancel.",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return AWAITING_CHANNEL_FORWARD
    except Exception as e:
        logging.error(f"Error in handle_forwarded_message: {str(e)}")
        update.message.reply_text(
            "\u26a0\ufe0f An error occurred while processing your request.\n"
            "Please try again or use /cancel to cancel the operation."
        )
        return AWAITING_CHANNEL_FORWARD

def cancel(update: Update, context: CallbackContext) -> int:
    """Cancel the current conversation."""
    user = update.effective_user
    user_id = user.id

    # Clear any active session data for this user
    if user_id in user_sessions:
        del user_sessions[user_id]

    # Create a keyboard with main commands
    keyboard = [
        [InlineKeyboardButton("📱 Add Channel", callback_data="cmd_addchannel")],
        [InlineKeyboardButton("📋 View Channels", callback_data="cmd_channels")],
        [InlineKeyboardButton("📊 Add Project", callback_data="cmd_addprojects")],
        [InlineKeyboardButton("⚙️ Manage Projects", callback_data="cmd_projects")]
    ]

    update.message.reply_text(
        f"🛑 *Operation Cancelled*\n\n"
        f"Your current operation has been cancelled and all temporary data has been cleared.\n\n"
        f"What would you like to do next?",
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode="Markdown"
    )

    return ConversationHandler.END

def list_channels(update: Update, context: CallbackContext) -> None:
    """List all channels added by the user."""
    channels = db.get_channels()

    if not channels:
        update.message.reply_text(
            "You haven't added any channels yet.\n"
            "Use /addchannel to add a channel."
        )
        return

    message = "📱 *Your Telegram Channels*\n\n"
    if channels:
        for idx, (channel_id, channel_data) in enumerate(channels.items(), 1):
            message += f"{idx}. *{channel_data['name']}* ({channel_data['type']})\n"

        message += "\n👉 Use /addprojects to create auto-posting projects for these channels."
    else:
        message += "⚠️ You haven't added any channels yet.\n\n"
        message += "👉 Use /addchannel to add your first channel."

    update.message.reply_text(message, parse_mode="Markdown")

def add_projects(update: Update, context: CallbackContext) -> int:
    """Start the process of adding a project."""
    channels = db.get_channels()

    if not channels:
        update.message.reply_text(
            "⚠️ *No Channels Found*\n\n"
            "Before creating a project, you need to add at least one channel.\n\n"
            "👉 Use /addchannel to add your Telegram channel first, then come back to create your project.",
            parse_mode="Markdown"
        )
        return ConversationHandler.END

    keyboard = []
    for channel_id, channel_data in channels.items():
        keyboard.append([
            InlineKeyboardButton(
                channel_data['name'],
                callback_data=f"select_channel:{channel_id}"
            )
        ])

    reply_markup = InlineKeyboardMarkup(keyboard)
    update.message.reply_text(
        "Select a channel for your auto-posting project:",
        reply_markup=reply_markup
    )

    return SELECTING_CHANNEL

def handle_channel_selection(update: Update, context: CallbackContext) -> int:
    """Handle the channel selection for a new project."""
    query = update.callback_query
    query.answer()

    # Extract channel_id from callback data
    channel_id = query.data.split(':')[1]
    user_id = query.from_user.id

    # Store the selected channel in user session
    if user_id not in user_sessions:
        user_sessions[user_id] = {}

    user_sessions[user_id]['selected_channel'] = channel_id

    # Show content type options
    keyboard = [
        [InlineKeyboardButton("DAILY NEWS SUMMARY", callback_data="content_type:daily_news_summary")],
        [InlineKeyboardButton("CRYPTO PRICES", callback_data="content_type:crypto_prices")],
        [InlineKeyboardButton("HEALTH & FITNESS", callback_data="content_type:health_fitness")]
        # More content types can be added here in the future
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        text="Please choose the type of auto posting among the list:",
        reply_markup=reply_markup
    )

    return SELECTING_CONTENT_TYPE

def handle_content_type_selection(update: Update, context: CallbackContext) -> int:
    """Handle the content type selection for a new project."""
    query = update.callback_query
    query.answer()

    # Extract content_type from callback data
    content_type = query.data.split(':')[1]
    user_id = query.from_user.id

    # Store the selected content type in user session
    user_sessions[user_id]['content_type'] = content_type

    query.edit_message_text(
        text="Please enter a name for your auto-posting project:"
    )

    return ENTERING_PROJECT_NAME

def handle_project_name(update: Update, context: CallbackContext) -> int:
    """Handle the project name input and create the project."""
    user_id = update.effective_user.id
    project_name = update.message.text.strip()

    if not project_name:
        update.message.reply_text("Please enter a valid project name.")
        return ENTERING_PROJECT_NAME

    # Get user session data
    session_data = user_sessions.get(user_id, {})
    channel_id = session_data.get('selected_channel')
    content_type = session_data.get('content_type')

    if not channel_id or not content_type:
        update.message.reply_text(
            "Something went wrong. Please start over with /addprojects."
        )
        return ConversationHandler.END

    # Get channel info
    channels = db.get_channels()
    channel_info = channels.get(channel_id, {})

    # Create project
    project_id = str(uuid.uuid4())
    channel_config = {
        "channel_id": channel_id,
        "channel_name": channel_info.get('name', 'Unknown'),
        "content_type": content_type
    }

    success = db.add_project(project_id, project_name, [channel_config])

    if success:
        # Configure content type based on selection
        if content_type == "daily_news_summary":
            # Configure Daily News Summary content type
            content_type_info = db.get_content_type("daily_news_summary")
            if content_type_info:
                settings = {
                    'country': content_type_info['parameters']['country'],
                    'post_interval_hours': content_type_info['parameters']['post_interval_hours']
                }
                db.update_project_content_settings(project_id, "daily_news_summary", settings)

                # Create keyboard for test posting
                keyboard = [
                    [InlineKeyboardButton(
                        "🚀 Test Post Daily News Summary",
                        callback_data=f"test_post:{project_id}"
                    )],
                    [InlineKeyboardButton(
                        "📋 View All Projects",
                        callback_data="back_to_projects"
                    )]
                ]

                update.message.reply_text(
                    f"✅ Project '{project_name}' has been created successfully!\n\n"
                    f"It will post Daily News Summary for India to {channel_info.get('name', 'the channel')}.\n\n"
                    f"You can test post now or manage your projects later.",
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
            else:
                update.message.reply_text(
                    f"✅ Project '{project_name}' has been created successfully!\n\n"
                    f"It will post Daily News Summary content to {channel_info.get('name', 'the channel')}.\n\n"
                    f"Use /projects to manage your projects."
                )
        elif content_type == "crypto_prices":
            # Configure Crypto Prices content type
            content_type_info = db.get_content_type("crypto_prices")
            if content_type_info:
                settings = {
                    'num_coins': content_type_info['parameters']['num_coins'],
                    'post_interval_hours': content_type_info['parameters']['post_interval_hours'],
                    'post_time_hour': content_type_info['parameters']['post_time_hour'],
                    'post_time_minute': content_type_info['parameters'].get('post_time_minute', 0),
                    'timezone_country': content_type_info['parameters'].get('timezone_country', 'India'),
                    'last_posted': None,
                    'image_settings': {
                        'mode': 'default',
                        'custom_image_path': None
                    }
                }
                db.update_project_content_settings(project_id, "crypto_prices", settings)

                # Create keyboard for test posting
                keyboard = [
                    [InlineKeyboardButton(
                        "🚀 Test Post Crypto Prices",
                        callback_data=f"test_post:{project_id}"
                    )],
                    [InlineKeyboardButton(
                        "📋 View All Projects",
                        callback_data="back_to_projects"
                    )]
                ]
            else:
                update.message.reply_text(
                    f"✅ Project '{project_name}' has been created successfully!\n\n"
                    f"It will post Crypto Prices content to {channel_info.get('name', 'the channel')}.\n\n"
                    f"Use /projects to manage your projects."
                )
        elif content_type == "health_fitness":
            # Configure Health & Fitness content type
            content_type_info = db.get_content_type("health_fitness")
            if content_type_info:
                settings = {
                    'post_interval_hours': content_type_info['parameters']['post_interval_hours'],
                    'post_time_hour': content_type_info['parameters']['post_time_hour'],
                    'post_time_minute': content_type_info['parameters'].get('post_time_minute', 0),
                    'timezone_country': content_type_info['parameters'].get('timezone_country', 'India'),
                    'last_posted': None,
                    'image_settings': {
                        'mode': 'default',  # Explicitly set to default mode
                        'custom_image_path': None
                    }
                }
                db.update_project_content_settings(project_id, "health_fitness", settings)

                # Create keyboard for test posting
                keyboard = [
                    [InlineKeyboardButton(
                        "🚀 Test Post Health & Fitness",
                        callback_data=f"test_post:{project_id}"
                    )],
                    [InlineKeyboardButton(
                        "📋 View All Projects",
                        callback_data="back_to_projects"
                    )]
                ]

                update.message.reply_text(
                    f"✅ Project '{project_name}' has been created successfully!\n\n"
                    f"It will post Health & Fitness content to {channel_info.get('name', 'the channel')}.\n\n"
                    f"You can test post now or manage your projects later.",
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
            else:
                update.message.reply_text(
                    f"✅ Project '{project_name}' has been created successfully!\n\n"
                    f"It will post Crypto Prices content to {channel_info.get('name', 'the channel')}.\n\n"
                    f"Use /projects to manage your projects."
                )
        else:
            update.message.reply_text(
                f"✅ Project '{project_name}' has been created successfully!\n\n"
                f"It will post {content_type} content to {channel_info.get('name', 'the channel')}.\n\n"
                f"Use /projects to manage your projects."
            )
    else:
        update.message.reply_text(
            "⚠️ Failed to create the project. Please try again."
        )

    # Clear user session
    if user_id in user_sessions:
        del user_sessions[user_id]

    return ConversationHandler.END

def list_projects(update: Update, context: CallbackContext) -> None:
    """List all projects created by the user."""
    projects = db.get_projects()

    if not projects:
        update.message.reply_text(
            "📅 *Your Auto-Posting Projects*\n\n"
            "⚠️ You don't have any auto-posting projects yet.\n\n"
            "👉 Use /addprojects to create your first daily news summary project.\n\n"
            "📝 *What are projects?*\n"
            "Projects automatically post daily news summaries to your channels at scheduled times.",
            parse_mode="Markdown"
        )
        return

    keyboard = []
    for project_id, project_data in projects.items():
        status = "✅ Active" if project_data.get('active', True) else "❌ Inactive"
        keyboard.append([
            InlineKeyboardButton(
                f"{project_data['name']} ({status})",
                callback_data=f"project_settings:{project_id}"
            )
        ])

    reply_markup = InlineKeyboardMarkup(keyboard)
    update.message.reply_text(
        "📋 Your auto-posting projects:\n"
        "Click on a project to manage its settings:",
        reply_markup=reply_markup
    )

def handle_project_settings(update: Update, context: CallbackContext) -> None:
    """Handle project settings selection."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content settings info
    content_settings = project_info.get('content_settings', {})

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Handle different content types
    if content_type == "daily_news_summary":
        # Auto-configure Daily News Summary if not already configured
        if 'daily_news_summary' not in content_settings:
            content_type_info = db.get_content_type("daily_news_summary")
            if content_type_info:
                settings = {
                    'country': content_type_info['parameters']['country'],
                    'post_interval_hours': content_type_info['parameters']['post_interval_hours'],
                    'post_time_hour': content_type_info['parameters']['post_time_hour'],
                    'post_time_minute': content_type_info['parameters'].get('post_time_minute', 0),
                    'timezone_country': content_type_info['parameters'].get('timezone_country', 'India'),
                    'last_posted': None
                }
                db.update_project_content_settings(project_id, "daily_news_summary", settings)
                content_settings = project_info.get('content_settings', {})

        # Get settings values for Daily News Summary
        news_settings = content_settings.get('daily_news_summary', {})
        interval = news_settings.get('post_interval_hours', 24)
        post_time_hour = news_settings.get('post_time_hour', 8)
        post_time_minute = news_settings.get('post_time_minute', 0)

        # Check if custom buttons are enabled
        custom_buttons = news_settings.get('custom_buttons', {'enabled': False, 'buttons': []})
        buttons_status = "Enabled" if custom_buttons.get('enabled', False) else "Disabled"
        button_count = len(custom_buttons.get('buttons', []))

        # Check image settings
        image_settings = news_settings.get('image_settings', {'custom_image_path': None})
        image_status = "Custom" if image_settings.get('custom_image_path') else "Default"

        # Get country setting
        country = news_settings.get('country', 'India')
        content_type_name = "Daily News Summary"
        content_details = f"• Type: {content_type_name}\n• Country: {country}"

    elif content_type == "crypto_prices":
        # Auto-configure Crypto Prices if not already configured
        if 'crypto_prices' not in content_settings:
            content_type_info = db.get_content_type("crypto_prices")
            if content_type_info:
                settings = {
                    'num_coins': content_type_info['parameters']['num_coins'],
                    'post_interval_hours': content_type_info['parameters']['post_interval_hours'],
                    'post_time_hour': content_type_info['parameters']['post_time_hour'],
                    'post_time_minute': content_type_info['parameters'].get('post_time_minute', 0),
                    'timezone_country': content_type_info['parameters'].get('timezone_country', 'India'),
                    'last_posted': None
                }
                db.update_project_content_settings(project_id, "crypto_prices", settings)
                content_settings = project_info.get('content_settings', {})

        # Get settings values for Crypto Prices
        crypto_settings = content_settings.get('crypto_prices', {})
        interval = crypto_settings.get('post_interval_hours', 24)
        post_time_hour = crypto_settings.get('post_time_hour', 9)
        post_time_minute = crypto_settings.get('post_time_minute', 0)

        # Check if custom buttons are enabled
        custom_buttons = crypto_settings.get('custom_buttons', {'enabled': False, 'buttons': []})
        buttons_status = "Enabled" if custom_buttons.get('enabled', False) else "Disabled"
        button_count = len(custom_buttons.get('buttons', []))

        # Check image settings
        image_settings = crypto_settings.get('image_settings', {'custom_image_path': None})
        image_status = "Custom" if image_settings.get('custom_image_path') else "Default"

        # Get number of coins setting
        num_coins = crypto_settings.get('num_coins', 4)
        content_type_name = "Crypto Prices"
        content_details = f"• Type: {content_type_name}\n• Main Coins: {num_coins}"

    elif content_type == "health_fitness":
        # Auto-configure Health & Fitness if not already configured
        if 'health_fitness' not in content_settings:
            content_type_info = db.get_content_type("health_fitness")
            if content_type_info:
                settings = {
                    'post_interval_hours': content_type_info['parameters']['post_interval_hours'],
                    'post_time_hour': content_type_info['parameters']['post_time_hour'],
                    'post_time_minute': content_type_info['parameters'].get('post_time_minute', 0),
                    'timezone_country': content_type_info['parameters'].get('timezone_country', 'India'),
                    'last_posted': None
                }
                db.update_project_content_settings(project_id, "health_fitness", settings)
                content_settings = project_info.get('content_settings', {})

        # Get settings values for Health & Fitness
        health_settings = content_settings.get('health_fitness', {})
        interval = health_settings.get('post_interval_hours', 24)
        post_time_hour = health_settings.get('post_time_hour', 9)
        post_time_minute = health_settings.get('post_time_minute', 0)

        # Check if custom buttons are enabled
        custom_buttons = health_settings.get('custom_buttons', {'enabled': False, 'buttons': []})
        buttons_status = "Enabled" if custom_buttons.get('enabled', False) else "Disabled"
        button_count = len(custom_buttons.get('buttons', []))

        # Check image settings
        image_settings = health_settings.get('image_settings', {'custom_image_path': None})
        image_status = "Custom" if image_settings.get('custom_image_path') else "Default"

        content_type_name = "Health & Fitness"
        content_details = f"• Type: {content_type_name}\n• Content: Health, Nutrition & Fitness"

    else:
        # Default settings for unknown content types
        interval = 24
        post_time_hour = 9
        post_time_minute = 0
        buttons_status = "Disabled"
        button_count = 0
        image_status = "Default"
        content_type_name = "Unknown"
        content_details = f"• Type: {content_type_name}"

    # Format time string
    time_str = f"{post_time_hour:02d}:{post_time_minute:02d}" if post_time_minute > 0 else f"{post_time_hour}:00"

    # Create settings keyboard
    status = project_info.get('active', True)

    # Get timezone country
    if content_type == "daily_news_summary" and "daily_news_summary" in content_settings:
        timezone_country = content_settings["daily_news_summary"].get("timezone_country", "India")
    elif content_type == "crypto_prices" and "crypto_prices" in content_settings:
        timezone_country = content_settings["crypto_prices"].get("timezone_country", "India")
    elif content_type == "health_fitness" and "health_fitness" in content_settings:
        timezone_country = content_settings["health_fitness"].get("timezone_country", "India")
    else:
        timezone_country = "India"

    keyboard = [
        [InlineKeyboardButton(
            "🔄 Toggle Status: " + ("Active" if status else "Inactive"),
            callback_data=f"toggle_project:{project_id}"
        )],
        [InlineKeyboardButton(
            "🚀 Post Now",
            callback_data=f"test_post:{project_id}"
        )],
        [InlineKeyboardButton(
            f"⏰ Posting Time: {time_str}",
            callback_data=f"set_post_time:{project_id}"
        )],
        [InlineKeyboardButton(
            f"🕓 Frequency: Every {interval} hours",
            callback_data=f"set_frequency:{project_id}"
        )],
        [InlineKeyboardButton(
            f"🌎 Time Zone: {timezone_country}",
            callback_data=f"set_timezone:{project_id}"
        )],
        [InlineKeyboardButton(
            "🔄 Reset Last Posted Time",
            callback_data=f"reset_last_posted:{project_id}"
        )],
        [InlineKeyboardButton(
            f"🔗 Custom Buttons: {buttons_status} ({button_count})",
            callback_data=f"custom_buttons:{project_id}"
        )],
        [InlineKeyboardButton(
            f"📷 Image Settings: {image_status}",
            callback_data=f"image_settings:{project_id}"
        )],
        [InlineKeyboardButton(
            "📃 View Post History",
            callback_data=f"view_history:{project_id}"
        )],
        [InlineKeyboardButton(
            "🗑️ Delete Project",
            callback_data=f"delete_project:{project_id}"
        )],
        [InlineKeyboardButton(
            "⬅️ Back to Projects",
            callback_data="back_to_projects"
        )]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)

    # Create project details message
    channels_info = ""
    for idx, channel in enumerate(project_info.get('channels', []), 1):
        channels_info += f"{idx}. {channel.get('channel_name', 'Unknown')}\n"

    # Get the appropriate settings based on content type
    if content_type == "daily_news_summary":
        last_posted = news_settings.get('last_posted')
    elif content_type == "crypto_prices":
        last_posted = crypto_settings.get('last_posted')
    elif content_type == "health_fitness":
        last_posted = health_settings.get('last_posted')
    else:
        last_posted = None

    # Format last posted time if available
    last_posted_info = "\nLast posted: Never" if not last_posted else f"\nLast posted: {last_posted}"

    message = (
        f"📊 *Project: {project_info['name']}*\n"
        f"Status: {'✅ Active' if status else '❌ Inactive'}\n\n"

        f"📱 *Channels:*\n{channels_info}\n"

        f"📰 *Content Settings:*\n"
        f"{content_details}\n\n"

        f"🕰️ *Schedule:*\n"
        f"• Time: {time_str} (24-hour format)\n"
        f"• Frequency: Every {interval} hours\n"
        f"• Time Zone: {timezone_country}{last_posted_info}\n\n"

        f"💻 *Select an action from the menu below:*"
    )

    query.edit_message_text(
        text=message,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )

def handle_toggle_project(update: Update, context: CallbackContext) -> None:
    """Toggle the active status of a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Toggle status
    current_status = project_info.get('active', True)
    new_status = not current_status

    # Update project
    db.update_project(project_id, {'active': new_status})

    # Redirect back to project settings
    query.edit_message_text(
        "Updating settings..."
    )

    # Call the project settings handler directly
    handle_project_settings(
        update,
        context
    )

def handle_back_to_projects(update: Update, context: CallbackContext) -> None:
    """Handle back to projects button."""
    query = update.callback_query
    query.answer()

    # Get all projects
    projects = db.get_projects()

    if not projects:
        query.edit_message_text(
            "You don't have any projects.\n"
            "Use /addprojects to create a project."
        )
        return

    keyboard = []
    for project_id, project_data in projects.items():
        status = "✅ Active" if project_data.get('active', True) else "❌ Inactive"
        keyboard.append([
            InlineKeyboardButton(
                f"{project_data['name']} ({status})",
                callback_data=f"project_settings:{project_id}"
            )
        ])

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        "📋 Your auto-posting projects:\n"
        "Click on a project to manage its settings:",
        reply_markup=reply_markup
    )

def handle_delete_project(update: Update, context: CallbackContext) -> None:
    """Handle project deletion."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Create confirmation keyboard
    keyboard = [
        [
            InlineKeyboardButton("✅ Yes, delete it", callback_data=f"confirm_delete:{project_id}"),
            InlineKeyboardButton("❌ No, keep it", callback_data=f"project_settings:{project_id}")
        ]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        f"Are you sure you want to delete the project '{project_info['name']}'?\n"
        f"This action cannot be undone.",
        reply_markup=reply_markup
    )

def handle_confirm_delete(update: Update, context: CallbackContext) -> None:
    """Handle confirmed project deletion."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info before deletion
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Delete the project
    success = db.remove_project(project_id)

    if success:
        query.edit_message_text(
            f"✅ Project '{project_info['name']}' has been deleted.\n\n"
            f"Use /projects to see your remaining projects or /addprojects to create a new one."
        )
    else:
        query.edit_message_text(
            "⚠️ Failed to delete the project. Please try again."
        )

def handle_content_settings(update: Update, context: CallbackContext) -> None:
    """Handle content settings for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content types
    content_types = db.get_content_types()

    # Create keyboard for content types
    keyboard = []
    for content_type_id, content_type_info in content_types.items():
        keyboard.append([
            InlineKeyboardButton(
                content_type_info['name'],
                callback_data=f"configure_content:{project_id}:{content_type_id}"
            )
        ])

    keyboard.append([
        InlineKeyboardButton(
            "⬅️ Back to Project Settings",
            callback_data=f"project_settings:{project_id}"
        )
    ])

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        "Select a content type to configure:",
        reply_markup=reply_markup
    )

def handle_configure_content(update: Update, context: CallbackContext) -> None:
    """Handle configuration of a specific content type."""
    query = update.callback_query
    query.answer()

    # Extract project_id and content_type_id from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    content_type_id = parts[2]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content type info
    content_type_info = db.get_content_type(content_type_id)

    if not content_type_info:
        query.edit_message_text("Content type not found.")
        return

    # Get current settings or use defaults
    content_settings = project_info.get('content_settings', {})
    current_settings = content_settings.get(content_type_id, {})

    # For now, we only support daily_news_summary
    if content_type_id == "daily_news_summary":
        # Get current values or defaults
        country = current_settings.get('country', content_type_info['parameters']['country'])
        interval = current_settings.get('post_interval_hours', content_type_info['parameters']['post_interval_hours'])

        # Create keyboard for country selection
        country_keyboard = [
            [InlineKeyboardButton("India", callback_data=f"set_country:{project_id}:{content_type_id}:India")],
            [InlineKeyboardButton("USA", callback_data=f"set_country:{project_id}:{content_type_id}:USA")],
            [InlineKeyboardButton("UK", callback_data=f"set_country:{project_id}:{content_type_id}:UK")],
            [InlineKeyboardButton("Australia", callback_data=f"set_country:{project_id}:{content_type_id}:Australia")],
            [InlineKeyboardButton("Canada", callback_data=f"set_country:{project_id}:{content_type_id}:Canada")]
        ]

        # Create keyboard for interval selection
        interval_keyboard = [
            [InlineKeyboardButton("Every 12 hours", callback_data=f"set_interval:{project_id}:{content_type_id}:12")],
            [InlineKeyboardButton("Every 24 hours", callback_data=f"set_interval:{project_id}:{content_type_id}:24")],
            [InlineKeyboardButton("Every 48 hours", callback_data=f"set_interval:{project_id}:{content_type_id}:48")]
        ]

        # Combine keyboards
        keyboard = [
            [InlineKeyboardButton(f"🌍 Country: {country}", callback_data=f"show_countries:{project_id}:{content_type_id}")],
            [InlineKeyboardButton(f"⏱️ Interval: Every {interval} hours", callback_data=f"show_intervals:{project_id}:{content_type_id}")],
            [InlineKeyboardButton("💾 Save Settings", callback_data=f"save_settings:{project_id}:{content_type_id}:{country}:{interval}")],
            [InlineKeyboardButton("⬅️ Back to Content Types", callback_data=f"content_settings:{project_id}")]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text(
            f"Configure {content_type_info['name']}:\n\n"
            f"Select the country and posting interval for the news summary.",
            reply_markup=reply_markup
        )

def handle_show_countries(update: Update, context: CallbackContext) -> None:
    """Show country selection options."""
    query = update.callback_query
    query.answer()

    # Extract project_id and content_type_id from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    content_type_id = parts[2]

    # Create keyboard for country selection
    keyboard = [
        [InlineKeyboardButton("India", callback_data=f"set_country:{project_id}:{content_type_id}:India")],
        [InlineKeyboardButton("USA", callback_data=f"set_country:{project_id}:{content_type_id}:USA")],
        [InlineKeyboardButton("UK", callback_data=f"set_country:{project_id}:{content_type_id}:UK")],
        [InlineKeyboardButton("Australia", callback_data=f"set_country:{project_id}:{content_type_id}:Australia")],
        [InlineKeyboardButton("Canada", callback_data=f"set_country:{project_id}:{content_type_id}:Canada")],
        [InlineKeyboardButton("⬅️ Back", callback_data=f"configure_content:{project_id}:{content_type_id}")]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        "Select a country for the news summary:",
        reply_markup=reply_markup
    )

def handle_show_intervals(update: Update, context: CallbackContext) -> None:
    """Show interval selection options."""
    query = update.callback_query
    query.answer()

    # Extract project_id and content_type_id from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    content_type_id = parts[2]

    # Create keyboard for interval selection
    keyboard = [
        [InlineKeyboardButton("Every 12 hours", callback_data=f"set_interval:{project_id}:{content_type_id}:12")],
        [InlineKeyboardButton("Every 24 hours", callback_data=f"set_interval:{project_id}:{content_type_id}:24")],
        [InlineKeyboardButton("Every 48 hours", callback_data=f"set_interval:{project_id}:{content_type_id}:48")],
        [InlineKeyboardButton("⬅️ Back", callback_data=f"configure_content:{project_id}:{content_type_id}")]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        "Select a posting interval:",
        reply_markup=reply_markup
    )

def handle_set_country(update: Update, context: CallbackContext) -> None:
    """Handle country selection."""
    query = update.callback_query
    query.answer()

    # Extract project_id, content_type_id, and country from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    content_type_id = parts[2]
    country = parts[3]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get current settings
    content_settings = project_info.get('content_settings', {})
    current_settings = content_settings.get(content_type_id, {})

    # Update country
    current_settings['country'] = country

    # Get interval (or use default)
    interval = current_settings.get('post_interval_hours', 24)

    # Redirect back to configure content
    keyboard = [
        [InlineKeyboardButton(f"🌍 Country: {country}", callback_data=f"show_countries:{project_id}:{content_type_id}")],
        [InlineKeyboardButton(f"⏱️ Interval: Every {interval} hours", callback_data=f"show_intervals:{project_id}:{content_type_id}")],
        [InlineKeyboardButton("💾 Save Settings", callback_data=f"save_settings:{project_id}:{content_type_id}:{country}:{interval}")],
        [InlineKeyboardButton("⬅️ Back to Content Types", callback_data=f"content_settings:{project_id}")]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        f"Country updated to {country}. Click 'Save Settings' to apply changes.",
        reply_markup=reply_markup
    )

def handle_set_interval(update: Update, context: CallbackContext) -> None:
    """Handle interval selection."""
    query = update.callback_query
    query.answer()

    # Extract project_id, content_type_id, and interval from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    content_type_id = parts[2]
    interval = int(parts[3])

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get current settings
    content_settings = project_info.get('content_settings', {})
    current_settings = content_settings.get(content_type_id, {})

    # Update interval
    current_settings['post_interval_hours'] = interval

    # Get country (or use default)
    country = current_settings.get('country', 'India')

    # Redirect back to configure content
    keyboard = [
        [InlineKeyboardButton(f"🌍 Country: {country}", callback_data=f"show_countries:{project_id}:{content_type_id}")],
        [InlineKeyboardButton(f"⏱️ Interval: Every {interval} hours", callback_data=f"show_intervals:{project_id}:{content_type_id}")],
        [InlineKeyboardButton("💾 Save Settings", callback_data=f"save_settings:{project_id}:{content_type_id}:{country}:{interval}")],
        [InlineKeyboardButton("⬅️ Back to Content Types", callback_data=f"content_settings:{project_id}")]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        f"Interval updated to {interval} hours. Click 'Save Settings' to apply changes.",
        reply_markup=reply_markup
    )

def handle_save_settings(update: Update, context: CallbackContext) -> None:
    """Save content settings for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id, content_type_id, country, and interval from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    content_type_id = parts[2]
    country = parts[3]
    interval = int(parts[4])

    # Save settings
    settings = {
        'country': country,
        'post_interval_hours': interval
    }

    success = db.update_project_content_settings(project_id, content_type_id, settings)

    if success:
        # Redirect back to project settings
        query.edit_message_text(
            "✅ Settings saved successfully!",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(
                    "⬅️ Back to Project Settings",
                    callback_data=f"project_settings:{project_id}"
                )
            ]])
        )
    else:
        query.edit_message_text(
            "⚠️ Failed to save settings. Please try again.",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(
                    "⬅️ Back to Project Settings",
                    callback_data=f"project_settings:{project_id}"
                )
            ]])
        )

def handle_test_post(update: Update, context: CallbackContext) -> None:
    """Handle test posting for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Check if content settings are configured
    content_settings = project_info.get('content_settings', {})

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Auto-configure content type if not already configured
    if content_type == "daily_news_summary" and 'daily_news_summary' not in content_settings:
        content_type_info = db.get_content_type("daily_news_summary")
        if content_type_info:
            settings = {
                'country': content_type_info['parameters']['country'],
                'post_interval_hours': content_type_info['parameters']['post_interval_hours'],
                'post_time_hour': content_type_info['parameters']['post_time_hour'],
                'last_posted': None
            }
            db.update_project_content_settings(project_id, "daily_news_summary", settings)
    elif content_type == "crypto_prices" and 'crypto_prices' not in content_settings:
        content_type_info = db.get_content_type("crypto_prices")
        if content_type_info:
            settings = {
                'num_coins': content_type_info['parameters']['num_coins'],
                'post_interval_hours': content_type_info['parameters']['post_interval_hours'],
                'post_time_hour': content_type_info['parameters']['post_time_hour'],
                'post_time_minute': content_type_info['parameters']['post_time_minute'],
                'timezone_country': content_type_info['parameters']['timezone_country'],
                'last_posted': None
            }
            db.update_project_content_settings(project_id, "crypto_prices", settings)

    # Show loading message based on content type
    if content_type == "daily_news_summary":
        query.edit_message_text(
            "🔄 Generating and posting Daily News Summary...\n"
            "This may take a moment."
        )
    elif content_type == "crypto_prices":
        query.edit_message_text(
            "🔄 Generating and posting Crypto Prices...\n"
            "This may take a moment."
        )
    else:
        query.edit_message_text(
            "🔄 Generating and posting content...\n"
            "This may take a moment."
        )

    # Attempt to post content with bypass_time_check=True
    success, message = poster.post_content(context.bot, project_id, test_mode=True, bypass_time_check=True)

    # Update last posted time if successful
    if success:
        content_settings = project_info.get('content_settings', {})
        if content_type == "daily_news_summary" and 'daily_news_summary' in content_settings:
            news_settings = content_settings['daily_news_summary']
            news_settings['last_posted'] = datetime.now().strftime("%Y-%m-%d %H:%M")
            db.update_project_content_settings(project_id, "daily_news_summary", news_settings)
        elif content_type == "crypto_prices" and 'crypto_prices' in content_settings:
            crypto_settings = content_settings['crypto_prices']
            crypto_settings['last_posted'] = datetime.now().strftime("%Y-%m-%d %H:%M")
            db.update_project_content_settings(project_id, "crypto_prices", crypto_settings)

    # Create back button
    keyboard = [[
        InlineKeyboardButton(
            "🚀 Post Again",
            callback_data=f"test_post:{project_id}"
        )
    ], [
        InlineKeyboardButton(
            "⬅️ Back to Project Settings",
            callback_data=f"project_settings:{project_id}"
        )
    ]]

    if success:
        query.edit_message_text(
            f"✅ News summary posted successfully!\n\n{message}",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        query.edit_message_text(
            f"⚠️ Posting failed:\n{message}",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

def handle_set_post_time(update: Update, context: CallbackContext) -> None:
    """Handle setting the posting time for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Create keyboard with time options
    keyboard = []
    for hour in range(0, 24, 3):  # Show options in 3-hour increments
        keyboard.append([
            InlineKeyboardButton(
                f"{hour}:00",
                callback_data=f"save_post_time:{project_id}:{hour}"
            )
        ])

    # Add custom time option
    keyboard.append([
        InlineKeyboardButton(
            "⏰ Custom Time...",
            callback_data=f"custom_post_time:{project_id}"
        )
    ])

    keyboard.append([
        InlineKeyboardButton(
            "⬅️ Back",
            callback_data=f"project_settings:{project_id}"
        )
    ])

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        "Select the hour when you want the daily news summary to be posted:\n"
        "(Times are in 24-hour format)",
        reply_markup=reply_markup
    )

def handle_custom_post_time(update: Update, context: CallbackContext) -> int:
    """Handle custom time input for a project."""
    query = update.callback_query
    query.answer()

    # Add debug logging
    logging.info(f"handle_custom_post_time called with data: {query.data}")

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Store project_id in user session
    user_id = query.from_user.id
    if user_id not in user_sessions:
        user_sessions[user_id] = {}
    user_sessions[user_id]['project_id'] = project_id
    user_sessions[user_id]['action'] = 'set_post_time'

    # Add more debug logging
    logging.info(f"User session updated for user {user_id}, project_id: {project_id}")

    query.edit_message_text(
        "Please enter a custom posting time in 24-hour format (HH:MM):\n\n"
        "Examples:\n"
        "9:00 = 9:00 AM\n"
        "14:30 = 2:30 PM\n"
        "21:15 = 9:15 PM\n\n"
        "You can also enter just the hour (e.g., 21 for 9:00 PM)"
    )

    return ENTERING_CUSTOM_TIME

def handle_custom_time_input(update: Update, context: CallbackContext) -> int:
    """Handle custom time input."""
    user_id = update.effective_user.id
    time_text = update.message.text.strip()

    # Add debug logging
    logging.info(f"Received custom time input: {time_text} from user {user_id}")

    # Parse time input
    hour = 0
    minute = 0

    try:
        # Check if input contains a colon (HH:MM format)
        if ':' in time_text:
            parts = time_text.split(':')
            if len(parts) != 2:
                raise ValueError("Invalid time format")

            hour = int(parts[0])
            minute = int(parts[1])
            logging.info(f"Parsed time input (HH:MM): hour={hour}, minute={minute}")

            # Validate hour and minute
            if hour < 0 or hour > 23 or minute < 0 or minute > 59:
                raise ValueError("Invalid time range")
        else:
            # Just hour format
            hour = int(time_text)
            minute = 0
            logging.info(f"Parsed time input (hour only): hour={hour}, minute={minute}")
            if hour < 0 or hour > 23:
                raise ValueError("Invalid hour range")
    except ValueError as e:
        update.message.reply_text(
            "\u26a0\ufe0f Invalid time format. Please enter time as HH:MM (e.g., 21:30) or just the hour (e.g., 21)."
        )
        return ENTERING_CUSTOM_TIME

    # Get session data
    if user_id not in user_sessions:
        update.message.reply_text("Session expired. Please start over.")
        return ConversationHandler.END

    project_id = user_sessions[user_id].get('project_id')

    if not project_id:
        update.message.reply_text("Session data missing. Please start over.")
        return ConversationHandler.END

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        update.message.reply_text("Project not found. It may have been deleted.")
        return ConversationHandler.END

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Update posting time in the appropriate content settings
    content_settings = project_info.get('content_settings', {})
    time_str = f"{hour:02d}:{minute:02d}" if minute > 0 else f"{hour}:00"

    logging.info(f"Setting custom time for project {project_id} to {time_str}")

    if content_type == "daily_news_summary" and "daily_news_summary" in content_settings:
        settings = content_settings["daily_news_summary"]
        settings["post_time_hour"] = int(hour)
        settings["post_time_minute"] = int(minute)
        db.update_project_content_settings(project_id, "daily_news_summary", settings)
        logging.info(f"Updated daily_news_summary posting time to {time_str} for project {project_id}")
        logging.info(f"Settings saved: post_time_hour={settings['post_time_hour']} (type: {type(settings['post_time_hour']).__name__}), post_time_minute={settings['post_time_minute']} (type: {type(settings['post_time_minute']).__name__})")
    elif content_type == "crypto_prices" and "crypto_prices" in content_settings:
        settings = content_settings["crypto_prices"]
        settings["post_time_hour"] = int(hour)
        settings["post_time_minute"] = int(minute)
        db.update_project_content_settings(project_id, "crypto_prices", settings)
        logging.info(f"Updated crypto_prices posting time to {time_str} for project {project_id}")
        logging.info(f"Settings saved: post_time_hour={settings['post_time_hour']} (type: {type(settings['post_time_hour']).__name__}), post_time_minute={settings['post_time_minute']} (type: {type(settings['post_time_minute']).__name__})")
    else:
        logging.warning(f"Could not update posting time for project {project_id} - content type {content_type} not found")

    # Restart the posting thread with the new time
    try:
        poster.restart_posting_thread(context.bot, project_id)
        logging.info(f"Posting thread restarted for project {project_id}")
    except Exception as e:
        logging.error(f"Error restarting posting thread: {str(e)}")

    # Send confirmation
    keyboard = [
        [InlineKeyboardButton(
            "\u2b05\ufe0f Back to Project Settings",
            callback_data=f"project_settings:{project_id}"
        )]
    ]

    update.message.reply_text(
        f"\u2705 Posting time updated to {time_str}",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    # Clear session data
    if user_id in user_sessions:
        del user_sessions[user_id]

    return ConversationHandler.END

def handle_save_post_time(update: Update, context: CallbackContext) -> None:
    """Save the posting time for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id and hour from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    hour = int(parts[2])

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Update posting time
    content_settings = project_info.get('content_settings', {})
    if 'daily_news_summary' in content_settings:
        news_settings = content_settings['daily_news_summary']
        news_settings['post_time_hour'] = int(hour)
        news_settings['post_time_minute'] = 0  # Default to 0 minutes for preset times
        db.update_project_content_settings(project_id, "daily_news_summary", news_settings)
        logging.info(f"Updated daily_news_summary posting time to {hour}:00 for project {project_id}")
        logging.info(f"Settings saved: post_time_hour={news_settings['post_time_hour']} (type: {type(news_settings['post_time_hour']).__name__}), post_time_minute={news_settings['post_time_minute']} (type: {type(news_settings['post_time_minute']).__name__})")
    elif 'crypto_prices' in content_settings:
        crypto_settings = content_settings['crypto_prices']
        crypto_settings['post_time_hour'] = int(hour)
        crypto_settings['post_time_minute'] = 0  # Default to 0 minutes for preset times
        db.update_project_content_settings(project_id, "crypto_prices", crypto_settings)
        logging.info(f"Updated crypto_prices posting time to {hour}:00 for project {project_id}")
        logging.info(f"Settings saved: post_time_hour={crypto_settings['post_time_hour']} (type: {type(crypto_settings['post_time_hour']).__name__}), post_time_minute={crypto_settings['post_time_minute']} (type: {type(crypto_settings['post_time_minute']).__name__})")

    # Restart the posting thread with the new time
    try:
        poster.restart_posting_thread(context.bot, project_id)
        logging.info(f"Posting thread restarted for project {project_id}")
    except Exception as e:
        logging.error(f"Error restarting posting thread: {str(e)}")

    # Redirect back to project settings
    query.edit_message_text(
        f"✅ Posting time updated to {hour}:00",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(
                "⬅️ Back to Project Settings",
                callback_data=f"project_settings:{project_id}"
            )
        ]])
    )

def handle_set_timezone(update: Update, context: CallbackContext) -> None:
    """Handle setting the timezone country for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Get all supported countries
    countries = tz.get_all_countries()

    # Create keyboard for country selection
    keyboard = []
    for country in countries:
        keyboard.append([InlineKeyboardButton(
            country,
            callback_data=f"save_timezone:{project_id}:{country}"
        )])

    keyboard.append([InlineKeyboardButton(
        "⬅️ Back to Project Settings",
        callback_data=f"project_settings:{project_id}"
    )])

    # Get current timezone country
    content_settings = project_info.get('content_settings', {})
    if content_type == "daily_news_summary" and "daily_news_summary" in content_settings:
        timezone_country = content_settings["daily_news_summary"].get("timezone_country", "India")
    elif content_type == "crypto_prices" and "crypto_prices" in content_settings:
        timezone_country = content_settings["crypto_prices"].get("timezone_country", "India")
    else:
        timezone_country = "India"

    # Get current time in each country
    country_times = {}
    for country in countries:
        country_times[country] = tz.format_time_for_country(country)

    # Create message
    message = f"🌎 *Select Time Zone (Country) for Project*\n\n"
    message += f"Current time zone: *{timezone_country}*\n"
    message += f"Current time in {timezone_country}: {country_times[timezone_country]}\n\n"
    message += f"Your project will post at the scheduled time in the selected country's time zone.\n\n"
    message += f"*Available Time Zones:*\n"

    # Add current time for each country
    for country in countries:
        message += f"• {country}: {country_times[country]}\n"

    query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode="Markdown"
    )

def handle_save_timezone(update: Update, context: CallbackContext) -> None:
    """Save the timezone country for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id and timezone_country from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    timezone_country = parts[2]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Update timezone country in the appropriate content settings
    content_settings = project_info.get('content_settings', {})
    if content_type == "daily_news_summary" and "daily_news_summary" in content_settings:
        settings = content_settings["daily_news_summary"]
        settings["timezone_country"] = timezone_country
        db.update_project_content_settings(project_id, "daily_news_summary", settings)
    elif content_type == "crypto_prices" and "crypto_prices" in content_settings:
        settings = content_settings["crypto_prices"]
        settings["timezone_country"] = timezone_country
        db.update_project_content_settings(project_id, "crypto_prices", settings)

    # Restart the posting thread with the new timezone
    try:
        poster.restart_posting_thread(context.bot, project_id)
        logging.info(f"Posting thread restarted for project {project_id} with new timezone: {timezone_country}")
    except Exception as e:
        logging.error(f"Error restarting posting thread: {str(e)}")

    # Show confirmation message
    query.edit_message_text(
        f"✅ Time zone updated to {timezone_country}.\n\nReturning to project settings..."
    )

    # Redirect back to project settings after a short delay
    time.sleep(1)
    handle_project_settings(update, context)

def handle_set_frequency(update: Update, context: CallbackContext) -> None:
    """Handle setting the posting frequency for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Create keyboard with frequency options
    keyboard = [
        [InlineKeyboardButton("Every 12 hours", callback_data=f"save_frequency:{project_id}:12")],
        [InlineKeyboardButton("Every 24 hours", callback_data=f"save_frequency:{project_id}:24")],
        [InlineKeyboardButton("Every 48 hours", callback_data=f"save_frequency:{project_id}:48")],
        [InlineKeyboardButton("⬅️ Back", callback_data=f"project_settings:{project_id}")]
    ]

    reply_markup = InlineKeyboardMarkup(keyboard)
    query.edit_message_text(
        "Select how often you want the news summary to be posted:",
        reply_markup=reply_markup
    )

def handle_save_frequency(update: Update, context: CallbackContext) -> None:
    """Save the posting frequency for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id and interval from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    interval = int(parts[2])

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Update posting frequency
    content_settings = project_info.get('content_settings', {})
    if 'daily_news_summary' in content_settings:
        news_settings = content_settings['daily_news_summary']
        news_settings['post_interval_hours'] = interval
        db.update_project_content_settings(project_id, "daily_news_summary", news_settings)

    # Restart the posting thread with the new frequency
    try:
        poster.restart_posting_thread(context.bot, project_id)
        logging.info(f"Posting thread restarted for project {project_id} with new frequency: {interval} hours")
    except Exception as e:
        logging.error(f"Error restarting posting thread: {str(e)}")

    # Redirect back to project settings
    query.edit_message_text(
        f"✅ Posting frequency updated to every {interval} hours",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(
                "⬅️ Back to Project Settings",
                callback_data=f"project_settings:{project_id}"
            )
        ]])
    )

def handle_custom_buttons(update: Update, context: CallbackContext) -> None:
    """Handle custom buttons settings for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})
    news_settings = content_settings.get('daily_news_summary', {})
    custom_buttons = news_settings.get('custom_buttons', {'enabled': False, 'buttons': []})

    # Create buttons list message
    buttons_status = "Enabled" if custom_buttons.get('enabled', False) else "Disabled"
    buttons = custom_buttons.get('buttons', [])

    message = f"🔗 Custom Buttons for '{project_info['name']}':\n\n"
    message += f"Status: {buttons_status}\n\n"

    if buttons:
        message += "Current buttons:\n"
        for i, button in enumerate(buttons, 1):
            message += f"{i}. {button['text']} → {button['url']}\n"
    else:
        message += "No buttons configured yet.\n"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton(
            f"{'Disable' if custom_buttons.get('enabled', False) else 'Enable'} Buttons",
            callback_data=f"toggle_buttons:{project_id}"
        )],
        [InlineKeyboardButton(
            "➕ Add Button",
            callback_data=f"add_button:{project_id}"
        )]
    ]

    # Add remove button options if there are buttons
    if buttons:
        keyboard.append([
            InlineKeyboardButton(
                "🗑️ Remove Button",
                callback_data=f"remove_button:{project_id}"
            )
        ])

    keyboard.append([
        InlineKeyboardButton(
            "⬅️ Back to Project Settings",
            callback_data=f"project_settings:{project_id}"
        )
    ])

    query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_toggle_buttons(update: Update, context: CallbackContext) -> None:
    """Toggle custom buttons on/off for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})
    news_settings = content_settings.get('daily_news_summary', {})
    custom_buttons = news_settings.get('custom_buttons', {'enabled': False, 'buttons': []})

    # Toggle enabled status
    custom_buttons['enabled'] = not custom_buttons.get('enabled', False)

    # Update settings
    news_settings['custom_buttons'] = custom_buttons
    db.update_project_content_settings(project_id, 'daily_news_summary', news_settings)

    # Redirect back to custom buttons settings
    query.edit_message_text(
        "Updating settings..."
    )

    # Call the custom buttons handler directly
    handle_custom_buttons(
        update,
        context
    )

def handle_add_button(update: Update, context: CallbackContext) -> int:
    """Start the process of adding a custom button."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Store project_id in user session
    user_id = query.from_user.id
    if user_id not in user_sessions:
        user_sessions[user_id] = {}
    user_sessions[user_id]['project_id'] = project_id

    query.edit_message_text(
        "Please enter the text for your button (max 30 characters):"
    )

    return ENTERING_BUTTON_TEXT

def handle_button_text(update: Update, context: CallbackContext) -> int:
    """Handle button text input."""
    user_id = update.effective_user.id
    button_text = update.message.text.strip()

    if not button_text:
        update.message.reply_text("Please enter valid button text.")
        return ENTERING_BUTTON_TEXT

    if len(button_text) > 30:
        update.message.reply_text("Button text is too long. Please keep it under 30 characters.")
        return ENTERING_BUTTON_TEXT

    # Store button text in user session
    if user_id not in user_sessions:
        update.message.reply_text("Session expired. Please start over.")
        return ConversationHandler.END

    user_sessions[user_id]['button_text'] = button_text

    update.message.reply_text(
        "Now please enter the URL for your button (must start with http:// or https://):"
    )

    return ENTERING_BUTTON_URL

def handle_button_url(update: Update, context: CallbackContext) -> int:
    """Handle button URL input and save the button."""
    user_id = update.effective_user.id
    button_url = update.message.text.strip()

    if not button_url.startswith(('http://', 'https://')):
        update.message.reply_text("Please enter a valid URL starting with http:// or https://")
        return ENTERING_BUTTON_URL

    # Get session data
    if user_id not in user_sessions:
        update.message.reply_text("Session expired. Please start over.")
        return ConversationHandler.END

    project_id = user_sessions[user_id].get('project_id')
    button_text = user_sessions[user_id].get('button_text')

    if not project_id or not button_text:
        update.message.reply_text("Session data missing. Please start over.")
        return ConversationHandler.END

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        update.message.reply_text("Project not found. It may have been deleted.")
        return ConversationHandler.END

    # Get content settings
    content_settings = project_info.get('content_settings', {})
    news_settings = content_settings.get('daily_news_summary', {})
    custom_buttons = news_settings.get('custom_buttons', {'enabled': False, 'buttons': []})

    # Add new button
    if 'buttons' not in custom_buttons:
        custom_buttons['buttons'] = []

    custom_buttons['buttons'].append({
        'text': button_text,
        'url': button_url
    })

    # Update settings
    news_settings['custom_buttons'] = custom_buttons
    db.update_project_content_settings(project_id, 'daily_news_summary', news_settings)

    # Send confirmation
    keyboard = [
        [InlineKeyboardButton(
            "🔙 Back to Custom Buttons",
            callback_data=f"custom_buttons:{project_id}"
        )]
    ]

    update.message.reply_text(
        f"✅ Button '{button_text}' added successfully!",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    # Clear session data
    if user_id in user_sessions:
        del user_sessions[user_id]

    return ConversationHandler.END

def handle_remove_button(update: Update, context: CallbackContext) -> None:
    """Handle removing a custom button."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})
    news_settings = content_settings.get('daily_news_summary', {})
    custom_buttons = news_settings.get('custom_buttons', {'enabled': False, 'buttons': []})
    buttons = custom_buttons.get('buttons', [])

    if not buttons:
        query.edit_message_text(
            "No buttons to remove.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton(
                    "⬅️ Back to Custom Buttons",
                    callback_data=f"custom_buttons:{project_id}"
                )]
            ])
        )
        return

    # Create keyboard with button removal options
    keyboard = []
    for i, button in enumerate(buttons):
        keyboard.append([
            InlineKeyboardButton(
                f"Remove: {button['text']}",
                callback_data=f"confirm_remove_button:{project_id}:{i}"
            )
        ])

    keyboard.append([
        InlineKeyboardButton(
            "⬅️ Back to Custom Buttons",
            callback_data=f"custom_buttons:{project_id}"
        )
    ])

    query.edit_message_text(
        "Select a button to remove:",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_confirm_remove_button(update: Update, context: CallbackContext) -> None:
    """Handle confirmation of button removal."""
    query = update.callback_query
    query.answer()

    # Extract project_id and button_index from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    button_index = int(parts[2])

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})
    news_settings = content_settings.get('daily_news_summary', {})
    custom_buttons = news_settings.get('custom_buttons', {'enabled': False, 'buttons': []})
    buttons = custom_buttons.get('buttons', [])

    if not buttons or button_index >= len(buttons):
        query.edit_message_text(
            "Button not found.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton(
                    "⬅️ Back to Custom Buttons",
                    callback_data=f"custom_buttons:{project_id}"
                )]
            ])
        )
        return

    # Remove the button
    removed_button = buttons.pop(button_index)

    # Update settings
    news_settings['custom_buttons'] = custom_buttons
    db.update_project_content_settings(project_id, 'daily_news_summary', news_settings)

    # Redirect back to custom buttons settings
    query.edit_message_text(
        "Updating settings..."
    )

    # Call the custom buttons handler directly
    handle_custom_buttons(
        update,
        context
    )

def handle_image_settings(update: Update, context: CallbackContext) -> None:
    """Handle image settings for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content settings and content type
    content_settings = project_info.get('content_settings', {})
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Get the appropriate settings based on content type
    if content_type == "daily_news_summary":
        settings = content_settings.get('daily_news_summary', {})
    elif content_type == "crypto_prices":
        settings = content_settings.get('crypto_prices', {})
    else:
        settings = {}

    # Get image settings
    image_settings = settings.get('image_settings', {
        'mode': 'default',  # 'default', 'custom', or 'internet'
        'custom_image_path': None
    })

    # Create message
    message = f"📷 Image Settings for '{project_info['name']}':\n\n"

    # Determine current mode
    mode = image_settings.get('mode', 'default')
    custom_image_path = image_settings.get('custom_image_path')

    if mode == 'custom' and custom_image_path:
        message += f"Mode: Custom Image\n"
        message += f"Using image: {os.path.basename(custom_image_path)}\n"
    elif mode == 'internet':
        message += f"Mode: Internet Images\n"
        message += f"Using random relevant images from the internet for each post\n"
    else:  # default
        message += f"Mode: Default Image\n"
        if content_type == "crypto_prices":
            message += f"Using default crypto image: {os.path.join('data/images', 'cryptoprices.png')}\n"
        elif content_type == "health_fitness":
            message += f"Using default health & fitness image: {os.path.join('data/images', 'healthfitness.png')}\n"
        else:
            message += f"Using default news image: {os.path.join('data/images', 'dailynewssummary.png')}\n"

    # Create keyboard with mode selection
    keyboard = [
        [InlineKeyboardButton(
            "🖼️ Use Default Image" + (" ✓" if mode == 'default' else ""),
            callback_data=f"set_image_mode:{project_id}:default"
        )],
        [InlineKeyboardButton(
            "📷 Use Custom Image" + (" ✓" if mode == 'custom' else ""),
            callback_data=f"set_image_mode:{project_id}:custom"
        )],
        [InlineKeyboardButton(
            "🌐 Use Internet Images" + (" ✓" if mode == 'internet' else ""),
            callback_data=f"set_image_mode:{project_id}:internet"
        )]
    ]

    # Add upload option if in custom mode
    if mode == 'custom':
        keyboard.append([
            InlineKeyboardButton(
                "📤 Upload Custom Image",
                callback_data=f"upload_image:{project_id}"
            )
        ])

    keyboard.append([
        InlineKeyboardButton(
            "⬅️ Back to Project Settings",
            callback_data=f"project_settings:{project_id}"
        )
    ])

    query.edit_message_text(
        message,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_toggle_images(update: Update, context: CallbackContext) -> None:
    """Toggle images on/off for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content settings
    content_settings = project_info.get('content_settings', {})
    news_settings = content_settings.get('daily_news_summary', {})
    image_settings = news_settings.get('image_settings', {'use_image': True, 'custom_image_path': None})

    # Toggle use_image status
    image_settings['use_image'] = not image_settings.get('use_image', True)

    # Update settings
    news_settings['image_settings'] = image_settings
    db.update_project_content_settings(project_id, 'daily_news_summary', news_settings)

    # Redirect back to image settings
    query.edit_message_text(
        "Updating settings..."
    )

    # Call the image settings handler directly
    handle_image_settings(
        update,
        context
    )

def handle_upload_image(update: Update, context: CallbackContext) -> int:
    """Start the process of uploading a custom image."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Store project_id in user session
    user_id = query.from_user.id
    if user_id not in user_sessions:
        user_sessions[user_id] = {}
    user_sessions[user_id]['project_id'] = project_id

    query.edit_message_text(
        "Please send me the image you want to use for this project. The image should be in JPG or PNG format."
    )

    return UPLOADING_IMAGE

def handle_image_upload(update: Update, context: CallbackContext) -> int:
    """Handle image upload and save it."""
    user_id = update.effective_user.id

    # Check if we have a photo
    if not update.message.photo:
        update.message.reply_text("Please send a valid image (JPG or PNG).")
        return UPLOADING_IMAGE

    # Get session data
    if user_id not in user_sessions:
        update.message.reply_text("Session expired. Please start over.")
        return ConversationHandler.END

    project_id = user_sessions[user_id].get('project_id')

    if not project_id:
        update.message.reply_text("Session data missing. Please start over.")
        return ConversationHandler.END

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        update.message.reply_text("Project not found. It may have been deleted.")
        return ConversationHandler.END

    try:
        # Get the largest photo (best quality)
        photo = update.message.photo[-1]

        # Download the photo
        photo_file = context.bot.get_file(photo.file_id)

        # Ensure the directory exists
        os.makedirs(db.IMAGE_DIR, exist_ok=True)

        # Generate a unique filename
        filename = f"project_{project_id}_{int(datetime.now().timestamp())}.jpg"
        file_path = os.path.join(db.IMAGE_DIR, filename)

        # Download the file
        photo_file.download(file_path)

        # Verify the file was downloaded successfully
        if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
            raise Exception("Failed to download the image file.")

        # Get content settings and content type
        content_settings = project_info.get('content_settings', {})
        content_type = None
        if project_info.get("channels"):
            content_type = project_info["channels"][0].get("content_type")

        # Get the appropriate settings based on content type
        if content_type == "daily_news_summary":
            settings = content_settings.get('daily_news_summary', {})
            content_type_name = 'daily_news_summary'
        elif content_type == "crypto_prices":
            settings = content_settings.get('crypto_prices', {})
            content_type_name = 'crypto_prices'
        elif content_type == "health_fitness":
            settings = content_settings.get('health_fitness', {})
            content_type_name = 'health_fitness'
        else:
            settings = {}
            content_type_name = 'daily_news_summary'  # Default

        # Get image settings
        image_settings = settings.get('image_settings', {'custom_image_path': None})

        # Update image settings
        image_settings['custom_image_path'] = file_path

        # Update settings
        settings['image_settings'] = image_settings
        db.update_project_content_settings(project_id, content_type_name, settings)

        # Send confirmation
        keyboard = [
            [InlineKeyboardButton(
                "🔙 Back to Image Settings",
                callback_data=f"image_settings:{project_id}"
            )]
        ]

        update.message.reply_text(
            f"✅ Image uploaded successfully! It will be used for future posts.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except IndexError:
        update.message.reply_text("Could not process the image. Please try sending a different image.")
        return UPLOADING_IMAGE

    except telegram_error.TelegramError as e:
        update.message.reply_text(f"Telegram error: {e}. Please try again with a different image.")
        return UPLOADING_IMAGE

    except Exception as e:
        logging.error(f"Error uploading image: {e}")
        update.message.reply_text(f"Error uploading image: {e}. Please try again.")
        return UPLOADING_IMAGE

    # Clear session data
    if user_id in user_sessions:
        del user_sessions[user_id]

    return ConversationHandler.END

def handle_set_image_mode(update: Update, context: CallbackContext) -> None:
    """Set the image mode for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id and mode from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    mode = parts[2]  # 'default', 'custom', or 'internet'

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content settings and content type
    content_settings = project_info.get('content_settings', {})
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Get the appropriate settings based on content type
    if content_type == "daily_news_summary":
        settings = content_settings.get('daily_news_summary', {})
    elif content_type == "crypto_prices":
        settings = content_settings.get('crypto_prices', {})
    elif content_type == "health_fitness":
        settings = content_settings.get('health_fitness', {})
    else:
        settings = {}

    # Get image settings
    image_settings = settings.get('image_settings', {
        'mode': 'default',
        'custom_image_path': None
    })

    # Update mode
    image_settings['mode'] = mode

    # Update settings
    settings['image_settings'] = image_settings

    # Save settings to the appropriate content type
    if content_type == "daily_news_summary":
        db.update_project_content_settings(project_id, 'daily_news_summary', settings)
    elif content_type == "crypto_prices":
        db.update_project_content_settings(project_id, 'crypto_prices', settings)
    elif content_type == "health_fitness":
        db.update_project_content_settings(project_id, 'health_fitness', settings)

    # Redirect back to image settings
    query.edit_message_text(
        "Updating settings..."
    )

    # Call the image settings handler directly
    handle_image_settings(
        update,
        context
    )

def handle_reset_image(update: Update, context: CallbackContext) -> None:
    """Reset to default image."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get content settings and content type
    content_settings = project_info.get('content_settings', {})
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Get the appropriate settings based on content type
    if content_type == "daily_news_summary":
        settings = content_settings.get('daily_news_summary', {})
        content_type_name = 'daily_news_summary'
    elif content_type == "crypto_prices":
        settings = content_settings.get('crypto_prices', {})
        content_type_name = 'crypto_prices'
    elif content_type == "health_fitness":
        settings = content_settings.get('health_fitness', {})
        content_type_name = 'health_fitness'
    else:
        settings = {}
        content_type_name = 'daily_news_summary'  # Default

    # Get image settings
    image_settings = settings.get('image_settings', {'custom_image_path': None})

    # Reset to default image
    image_settings['custom_image_path'] = None

    # Update settings
    settings['image_settings'] = image_settings
    db.update_project_content_settings(project_id, content_type_name, settings)

    # Redirect back to image settings
    query.edit_message_text(
        "Updating settings..."
    )

    # Call the image settings handler directly
    handle_image_settings(
        update,
        context
    )

def handle_cancel_add_channel(update: Update, context: CallbackContext) -> int:
    """Handle cancellation of channel addition from inline button."""
    query = update.callback_query
    query.answer()

    query.edit_message_text(
        "\ud83d\uded1 Channel addition cancelled.\n\n"
        "You can use /addchannel again when you're ready to add a channel."
    )

    return ConversationHandler.END

def handle_command_buttons(update: Update, context: CallbackContext) -> None:
    """Handle command buttons from the cancel menu."""
    query = update.callback_query
    query.answer()

    command = query.data.split('_')[1]

    if command == "addchannel":
        query.edit_message_text("Please use /addchannel to add a new channel.")
    elif command == "channels":
        # Show channels list
        channels = db.get_channels()

        if not channels:
            query.edit_message_text(
                "You haven't added any channels yet.\n"
                "Use /addchannel to add a channel."
            )
            return

        message = "📱 *Your Telegram Channels*\n\n"
        for idx, (channel_id, channel_data) in enumerate(channels.items(), 1):
            message += f"{idx}. *{channel_data['name']}* ({channel_data['type']})\n"

        message += "\n👉 Use /addprojects to create auto-posting projects for these channels."

        query.edit_message_text(message, parse_mode="Markdown")
    elif command == "addprojects":
        query.edit_message_text("Please use /addprojects to create a new project.")
    elif command == "projects":
        # Show projects list
        projects = db.get_projects()

        if not projects:
            query.edit_message_text(
                "You don't have any projects.\n"
                "Use /addprojects to create a project."
            )
            return

        keyboard = []
        for project_id, project_data in projects.items():
            status = "✅ Active" if project_data.get('active', True) else "❌ Inactive"
            keyboard.append([
                InlineKeyboardButton(
                    f"{project_data['name']} ({status})",
                    callback_data=f"project_settings:{project_id}"
                )
            ])

        reply_markup = InlineKeyboardMarkup(keyboard)
        query.edit_message_text(
            "📋 Your auto-posting projects:\n"
            "Click on a project to manage its settings:",
            reply_markup=reply_markup
        )

def handle_view_history(update: Update, context: CallbackContext) -> None:
    """Handle viewing post history for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text("Project not found. It may have been deleted.")
        return

    # Get post history
    history = db.get_post_history().get(project_id, [])

    if not history:
        query.edit_message_text(
            "No posting history found for this project.",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(
                    "⬅️ Back to Project Settings",
                    callback_data=f"project_settings:{project_id}"
                )
            ]])
        )
        return

    # Format history (show last 5 posts)
    history_text = "Recent posting history:\n\n"
    for entry in history[-5:]:
        timestamp = entry.get('timestamp', 'Unknown time')
        channel_id = entry.get('channel_id', 'Unknown channel')
        channel_name = "Unknown"

        # Try to find channel name
        for channel_config in project_info.get('channels', []):
            if channel_config.get('channel_id') == channel_id:
                channel_name = channel_config.get('channel_name', 'Unknown')
                break

        history_text += f"• {timestamp} - Posted to {channel_name}\n"

    # Add buttons for navigation and clearing history
    keyboard = [
        [InlineKeyboardButton(
            "⬅️ Back to Project Settings",
            callback_data=f"project_settings:{project_id}"
        )],
        [InlineKeyboardButton(
            "❌ Clear Posting History",
            callback_data=f"clear_history:{project_id}"
        )]
    ]

    query.edit_message_text(
        history_text,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def show_server_time(update: Update, context: CallbackContext) -> None:
    """Show the current server time and times in different countries."""
    # Get server time
    server_time = datetime.now()
    formatted_server_time = server_time.strftime("%Y-%m-%d %H:%M:%S")

    # Get times for different countries
    message = f"⏰ *Current Server Time:* {formatted_server_time}\n\n"

    # Add times for different countries
    for country in tz.get_all_countries():
        country_time = tz.format_time_for_country(country)
        message += f"*{country}:* {country_time}\n"

    update.message.reply_text(message, parse_mode="Markdown")

def check_posting_status(update: Update, context: CallbackContext) -> None:
    """Check the status of posting threads."""
    # Check if the user provided a project ID
    if context.args and len(context.args) > 0:
        project_id = context.args[0]
        status = poster.get_posting_thread_status(project_id)

        if "error" in status:
            update.message.reply_text(f"❌ {status['error']}")
            return

        message = f"ℹ️ *Posting Thread Status for Project*\n\n"
        message += f"Project ID: `{status['project_id']}`\n"
        message += f"Thread Active: {status['thread_alive']}\n"
        message += f"Thread Stopped: {status['thread_stopped']}\n"
        message += f"Content Type: {status['content_type']}\n"
        message += f"Posting Time: {status['post_time']} ({status['timezone_country']} time)\n"
        message += f"Current Time: {status['current_time']} ({status['timezone_country']} time)\n"
        message += f"Last Posted: {status['last_posted'] or 'Never'}\n"

        update.message.reply_text(message, parse_mode="Markdown")
    else:
        # No project ID provided, show status for all projects
        status = poster.get_posting_thread_status()

        if not status:
            update.message.reply_text("No active posting threads found.")
            return

        message = f"ℹ️ *All Posting Threads Status*\n\n"

        for project_id, project_status in status.items():
            message += f"Project: `{project_id}`\n"
            message += f"Thread Active: {project_status['thread_alive']}\n"
            message += f"Posting Time: {project_status['post_time']} ({project_status['timezone_country']} time)\n"
            message += f"Current Time: {project_status['current_time']} ({project_status['timezone_country']} time)\n\n"

        update.message.reply_text(message, parse_mode="Markdown")

def reset_last_posted_command(update: Update, context: CallbackContext) -> None:
    """Reset the last posted time for a project."""
    # Check if a project ID was provided
    if not context.args or len(context.args) < 1:
        # Get all projects
        projects = db.get_projects()

        if not projects:
            update.message.reply_text("No projects found.")
            return

        # Create a list of projects for the user to choose from
        project_list = "ℹ️ **Reset Last Posted Time**\n\n"
        project_list += "This command resets the last posted time for a project, allowing it to post immediately at the next scheduled time.\n\n"
        project_list += "Please specify a project ID:\n\n"
        for project_id, project_info in projects.items():
            project_name = project_info.get("name", "Unknown Project")
            project_list += f"📌 {project_name}: `/resetlast {project_id}`\n"

        update.message.reply_text(project_list, parse_mode='Markdown')
        return

    # Get the project ID from the command arguments
    project_id = context.args[0]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        update.message.reply_text(f"Project with ID {project_id} not found.")
        return

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    if not content_type:
        update.message.reply_text(f"No content type found for project {project_id}.")
        return

    # Reset the last posted time
    success = db.reset_last_posted_time(project_id, content_type)

    # Also reset the last post time in the content_poster module
    if project_id in poster.last_post_times:
        del poster.last_post_times[project_id]

    if success:
        # Restart the posting thread with the new time
        try:
            poster.restart_posting_thread(update.effective_message.bot, project_id)
            logging.info(f"Posting thread restarted for project {project_id} after resetting last posted time")
        except Exception as e:
            logging.error(f"Error restarting posting thread: {str(e)}")

        project_name = project_info.get("name", "Unknown Project")
        update.message.reply_text(
            f"✅ Last posted time has been reset successfully for project '{project_name}'.\n\n"
            "The bot will now post at the next scheduled time without waiting for the minimum time between posts."
        )
    else:
        update.message.reply_text("❌ Failed to reset last posted time. Please try again later.")

def manual_post_command(update: Update, context: CallbackContext) -> None:
    """Manually trigger a post for a project."""

    # Check if the user provided a project ID
    if not context.args or len(context.args) < 1:
        # No project ID provided, show a list of projects
        projects = db.get_projects()

        if not projects:
            update.message.reply_text("You don't have any projects yet. Use /addprojects to create one.")
            return

        keyboard = []
        for project_id, project_info in projects.items():
            project_name = project_info.get("name", "Unnamed Project")
            keyboard.append([InlineKeyboardButton(
                f"{project_name}",
                callback_data=f"manual_post:{project_id}"
            )])

        keyboard.append([InlineKeyboardButton("Cancel", callback_data="cancel_manual_post")])

        update.message.reply_text(
            "Select a project to manually post content:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        # Project ID provided, try to post
        project_id = context.args[0]
        success, message = poster.manual_post(context.bot, project_id)

        if success:
            update.message.reply_text(f"✅ Successfully posted content: {message}")
        else:
            update.message.reply_text(f"❌ Failed to post content: {message}")

def handle_manual_post(update: Update, context: CallbackContext) -> None:
    """Handle manual post button click."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Show a processing message
    query.edit_message_text("Processing your request... Please wait.")

    # Trigger the post
    success, message = poster.manual_post(context.bot, project_id)

    # Show the result
    if success:
        query.edit_message_text(f"✅ Successfully posted content: {message}")
    else:
        query.edit_message_text(f"❌ Failed to post content: {message}")

def handle_cancel_manual_post(update: Update, context: CallbackContext) -> None:
    """Handle cancellation of manual post."""
    query = update.callback_query
    query.answer()

    query.edit_message_text("Manual posting cancelled.")

def handle_clear_history(update: Update, context: CallbackContext) -> None:
    """Handle clearing post history for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Show confirmation dialog
    keyboard = [
        [InlineKeyboardButton(
            "✅ Yes, Clear History",
            callback_data=f"confirm_clear_history:{project_id}"
        )],
        [InlineKeyboardButton(
            "❌ No, Cancel",
            callback_data=f"view_history:{project_id}"
        )]
    ]

    # Just send a new message without trying to delete the old one
    # This avoids issues with malformed buttons in the original message

    context.bot.send_message(
        chat_id=query.message.chat_id,
        text="⚠️ Are you sure you want to clear the posting history for this project?\n\n"
        "This action cannot be undone.",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_confirm_clear_history(update: Update, context: CallbackContext) -> None:
    """Handle confirmation of clearing post history."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Clear the history
    success = db.clear_post_history(project_id)

    # Just answer the callback query without trying to delete the message
    # This avoids issues with malformed buttons in the original message

    if success:
        # Get project info for the back button
        projects = db.get_projects()
        project_info = projects.get(project_id, {})

        if not project_info:
            context.bot.send_message(
                chat_id=query.message.chat_id,
                text="Project not found. It may have been deleted."
            )
            return

        # Show success message
        keyboard = [
            [InlineKeyboardButton(
                "⬅️ Back to Project Settings",
                callback_data=f"project_settings:{project_id}"
            )]
        ]

        context.bot.send_message(
            chat_id=query.message.chat_id,
            text="✅ Posting history has been cleared successfully.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        # Show error message
        keyboard = [
            [InlineKeyboardButton(
                "⬅️ Back to Project Settings",
                callback_data=f"project_settings:{project_id}"
            )]
        ]

        context.bot.send_message(
            chat_id=query.message.chat_id,
            text="❌ Failed to clear posting history. Please try again later.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

def handle_reset_last_posted(update: Update, context: CallbackContext) -> None:
    """Handle resetting the last posted time for a project."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        # Just send a new message instead of trying to edit
        context.bot.send_message(
            chat_id=query.message.chat_id,
            text="Project not found. It may have been deleted."
        )
        return

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Instead of showing a confirmation dialog, directly reset the last posted time
    # This avoids issues with malformed buttons
    if content_type:
        # Reset the last posted time
        success = db.reset_last_posted_time(project_id, content_type)

        # Also reset the last post time in the content_poster module
        if project_id in poster.last_post_times:
            del poster.last_post_times[project_id]

        if success:
            # Restart the posting thread with the new time
            try:
                poster.restart_posting_thread(context.bot, project_id)
                logging.info(f"Posting thread restarted for project {project_id} after resetting last posted time")
            except Exception as e:
                logging.error(f"Error restarting posting thread: {str(e)}")

            # Show success message
            context.bot.send_message(
                chat_id=query.message.chat_id,
                text="✅ Last posted time has been reset successfully.\n\n"
                "The bot will now post at the next scheduled time without waiting for the minimum time between posts."
            )
        else:
            # Show error message
            context.bot.send_message(
                chat_id=query.message.chat_id,
                text="❌ Failed to reset last posted time. Please try again later or use the /resetlast command."
            )
    else:
        context.bot.send_message(
            chat_id=query.message.chat_id,
            text="❌ Could not determine content type for this project. Please use the /resetlast command instead."
        )

def handle_confirm_reset_last_posted(update: Update, context: CallbackContext) -> None:
    """Handle confirmation of resetting the last posted time."""
    query = update.callback_query
    query.answer()

    # Extract project_id and content_type from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    content_type = parts[2]

    # Reset the last posted time
    success = db.reset_last_posted_time(project_id, content_type)

    # Also reset the last post time in the content_poster module
    if project_id in poster.last_post_times:
        del poster.last_post_times[project_id]

    # Just answer the callback query without trying to delete the message
    # This avoids issues with malformed buttons in the original message

    if success:
        # Get project info for the back button
        projects = db.get_projects()
        project_info = projects.get(project_id, {})

        if not project_info:
            context.bot.send_message(
                chat_id=query.message.chat_id,
                text="Project not found. It may have been deleted."
            )
            return

        # Restart the posting thread with the new time
        try:
            poster.restart_posting_thread(context.bot, project_id)
            logging.info(f"Posting thread restarted for project {project_id} after resetting last posted time")
        except Exception as e:
            logging.error(f"Error restarting posting thread: {str(e)}")

        # Show success message
        keyboard = [
            [InlineKeyboardButton(
                "⬅️ Back to Project Settings",
                callback_data=f"project_settings:{project_id}"
            )]
        ]

        context.bot.send_message(
            chat_id=query.message.chat_id,
            text="✅ Last posted time has been reset successfully.\n\n"
            "The bot will now post at the next scheduled time without waiting for the minimum time between posts.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        # Show error message
        keyboard = [
            [InlineKeyboardButton(
                "⬅️ Back to Project Settings",
                callback_data=f"project_settings:{project_id}"
            )]
        ]

        context.bot.send_message(
            chat_id=query.message.chat_id,
            text="❌ Failed to reset last posted time. Please try again later.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )


def error_handler(update: Update, context: CallbackContext) -> None:
    """Log errors caused by updates."""
    try:
        # Log the error
        error_msg = str(context.error) if context and hasattr(context, 'error') else "Unknown error"
        logger.error(f'Update "{update}" caused error: {error_msg}')

        # Get the exception traceback
        import traceback
        tb_list = traceback.format_exception(None, context.error, context.error.__traceback__) if context and hasattr(context, 'error') else []
        tb_string = ''.join(tb_list)
        logger.error(f"Exception traceback:\n{tb_string}")

        # Notify user only if update is not None and has an effective_message
        if update and hasattr(update, 'effective_message') and update.effective_message:
            update.effective_message.reply_text(
                "⚠️ An error occurred while processing your request.\n"
                "The error has been logged and will be addressed.\n"
                "Please try again or use /cancel to reset the conversation."
            )

    except Exception as e:
        # If error handling itself fails, log it but don't crash
        logger.error(f"Error in error handler: {str(e)}")

def main() -> None:
    """Start the bot."""
    # Get the bot token from environment variable
    token = os.getenv("TELEGRAM_BOT_TOKEN")

    if not token:
        logger.error("No bot token found. Please set the TELEGRAM_BOT_TOKEN environment variable.")
        return

    # Create the Updater and pass it your bot's token
    updater = Updater(token)

    # Get the dispatcher to register handlers
    dispatcher = updater.dispatcher

    # Add conversation handler for adding channels
    add_channel_conv = ConversationHandler(
        entry_points=[CommandHandler("addchannel", add_channel)],
        states={
            AWAITING_CHANNEL_FORWARD: [
                MessageHandler(Filters.all & ~Filters.command, handle_forwarded_message),
                CallbackQueryHandler(handle_cancel_add_channel, pattern=r"^cancel_add_channel$"),
                CommandHandler("cancel", cancel)
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)]
    )

    # Add conversation handler for adding projects
    add_project_conv = ConversationHandler(
        entry_points=[CommandHandler("addprojects", add_projects)],
        states={
            SELECTING_CHANNEL: [
                CallbackQueryHandler(handle_channel_selection, pattern=r"^select_channel:")
            ],
            SELECTING_CONTENT_TYPE: [
                CallbackQueryHandler(handle_content_type_selection, pattern=r"^content_type:")
            ],
            ENTERING_PROJECT_NAME: [
                MessageHandler(Filters.text & ~Filters.command, handle_project_name)
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)]
    )

    # Add conversation handler for custom time input first (priority)
    logging.info("Registering custom time input conversation handler (high priority)")
    custom_time_conv = ConversationHandler(
        entry_points=[CallbackQueryHandler(handle_custom_post_time, pattern=r"^custom_post_time:")],
        states={
            ENTERING_CUSTOM_TIME: [
                MessageHandler(Filters.text & ~Filters.command, handle_custom_time_input)
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)],
        name="custom_time_conversation"
    )
    dispatcher.add_handler(custom_time_conv)
    logging.info("Custom time input conversation handler registered successfully")

    # Register handlers
    dispatcher.add_handler(CommandHandler("start", start))
    dispatcher.add_handler(CommandHandler("help", help_command))
    dispatcher.add_handler(CommandHandler("cancel", cancel))
    dispatcher.add_handler(CommandHandler("channels", list_channels))
    dispatcher.add_handler(CommandHandler("projects", list_projects))
    dispatcher.add_handler(CommandHandler("time", show_server_time))
    dispatcher.add_handler(CommandHandler("post", manual_post_command))
    dispatcher.add_handler(CommandHandler("status", check_posting_status))
    dispatcher.add_handler(CommandHandler("resetlast", reset_last_posted_command))
    dispatcher.add_handler(add_channel_conv)
    dispatcher.add_handler(add_project_conv)

    # Add command button handlers
    dispatcher.add_handler(CallbackQueryHandler(handle_command_buttons, pattern=r"^cmd_"))

    # Register callback query handlers
    dispatcher.add_handler(CallbackQueryHandler(handle_project_settings, pattern=r"^project_settings:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_toggle_project, pattern=r"^toggle_project:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_delete_project, pattern=r"^delete_project:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_confirm_delete, pattern=r"^confirm_delete:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_back_to_projects, pattern=r"^back_to_projects$"))

    # Register manual posting handlers
    dispatcher.add_handler(CallbackQueryHandler(handle_manual_post, pattern=r"^manual_post:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_cancel_manual_post, pattern=r"^cancel_manual_post$"))

    # Register content settings handlers
    dispatcher.add_handler(CallbackQueryHandler(handle_content_settings, pattern=r"^content_settings:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_configure_content, pattern=r"^configure_content:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_show_countries, pattern=r"^show_countries:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_show_intervals, pattern=r"^show_intervals:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_set_country, pattern=r"^set_country:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_set_interval, pattern=r"^set_interval:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_save_settings, pattern=r"^save_settings:"))

    # Register test posting handler
    dispatcher.add_handler(CallbackQueryHandler(handle_test_post, pattern=r"^test_post:"))

    # Register new scheduling handlers
    dispatcher.add_handler(CallbackQueryHandler(handle_set_post_time, pattern=r"^set_post_time:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_save_post_time, pattern=r"^save_post_time:"))
    # Note: handle_custom_post_time is registered in the conversation handler below
    dispatcher.add_handler(CallbackQueryHandler(handle_set_frequency, pattern=r"^set_frequency:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_save_frequency, pattern=r"^save_frequency:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_set_timezone, pattern=r"^set_timezone:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_save_timezone, pattern=r"^save_timezone:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_view_history, pattern=r"^view_history:"))

    # Register history management handlers
    dispatcher.add_handler(CallbackQueryHandler(handle_clear_history, pattern=r"^clear_history:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_confirm_clear_history, pattern=r"^confirm_clear_history:"))

    # Register last posted time reset handlers
    dispatcher.add_handler(CallbackQueryHandler(handle_reset_last_posted, pattern=r"^reset_last_posted:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_confirm_reset_last_posted, pattern=r"^confirm_reset_last_posted:"))

    # Register custom buttons handlers
    dispatcher.add_handler(CallbackQueryHandler(handle_custom_buttons, pattern=r"^custom_buttons:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_toggle_buttons, pattern=r"^toggle_buttons:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_remove_button, pattern=r"^remove_button:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_confirm_remove_button, pattern=r"^confirm_remove_button:"))

    # Register image settings handlers
    dispatcher.add_handler(CallbackQueryHandler(handle_image_settings, pattern=r"^image_settings:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_set_image_mode, pattern=r"^set_image_mode:"))
    dispatcher.add_handler(CallbackQueryHandler(handle_reset_image, pattern=r"^reset_image:"))

    # Add conversation handler for adding buttons
    add_button_conv = ConversationHandler(
        entry_points=[CallbackQueryHandler(handle_add_button, pattern=r"^add_button:")],
        states={
            ENTERING_BUTTON_TEXT: [
                MessageHandler(Filters.text & ~Filters.command, handle_button_text)
            ],
            ENTERING_BUTTON_URL: [
                MessageHandler(Filters.text & ~Filters.command, handle_button_url)
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)]
    )
    dispatcher.add_handler(add_button_conv)

    # Custom time input conversation handler is already registered above with high priority

    # Add conversation handler for uploading images
    upload_image_conv = ConversationHandler(
        entry_points=[CallbackQueryHandler(handle_upload_image, pattern=r"^upload_image:")],
        states={
            UPLOADING_IMAGE: [
                MessageHandler(Filters.photo, handle_image_upload),
                MessageHandler(Filters.text & ~Filters.command, lambda update, _: update.message.reply_text("Please send an image, not text."))
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel)]
    )
    dispatcher.add_handler(upload_image_conv)

    # Register error handler
    dispatcher.add_error_handler(error_handler)

    # Start the Bot
    updater.start_polling()
    logging.info("Bot started successfully and is now polling for updates.")

    # Start content posting threads for active projects
    poster.start_all_active_projects(updater.bot)
    logging.info("All active project posting threads started.")

    # Run the bot until you press Ctrl-C
    try:
        logging.info("Bot is running. Press Ctrl+C to stop.")
        updater.idle()
    except KeyboardInterrupt:
        logging.info("Received keyboard interrupt. Shutting down...")
    finally:
        # Stop all posting threads when the bot is stopped
        logging.info("Stopping all posting threads...")
        poster.stop_all_posting_threads()
        logging.info("Shutdown complete.")

if __name__ == "__main__":
    main()
