"""
Telegram Auto-Posting Bot - Main Entry Point
"""
import os
import logging
from dotenv import load_dotenv
from telegram.ext import (
    Application,
    CommandHandler,
    CallbackQueryHandler,
)

# Import database and content poster modules
import database as db
import content_poster as poster

# Import handlers
from handlers.command_handlers import (
    start,
    help_command,
    cancel,
    show_server_time,
    check_posting_status,
    reset_last_posted_command,
    handle_command_buttons,
)
from handlers.channel_handlers import list_channels
from handlers.project_handlers import (
    list_projects,
    handle_project_settings,
    handle_toggle_project,
    handle_delete_project,
    handle_confirm_delete,
    handle_back_to_projects,
    handle_test_post,
    manual_post_command,
    handle_manual_post,
    handle_cancel_manual_post,
    handle_view_history,
    handle_clear_history,
    handle_confirm_clear_history,
    handle_confirm_reset_last_posted,
)
from handlers.settings_handlers import (
    handle_content_settings,
    handle_configure_content,
    handle_show_countries,
    handle_show_intervals,
    handle_set_country,
    handle_set_interval,
    handle_save_settings,
    handle_set_num_coins,
    handle_set_post_time,
    handle_save_post_time,
    handle_set_timezone,
    handle_save_timezone,
)
from handlers.button_handlers import (
    handle_custom_buttons,
    handle_toggle_buttons,
    handle_remove_button,
    handle_confirm_remove_button,
)
from handlers.image_handlers import (
    handle_image_settings,
    handle_set_image_mode,
    handle_reset_image,
)
from handlers.time_handlers import (
    handle_set_frequency,
    handle_save_frequency,
    handle_reset_last_posted,
)
from handlers.conversation_handlers import (
    get_add_channel_conversation_handler,
    get_add_project_conversation_handler,
    get_custom_time_conversation_handler,
    get_add_button_conversation_handler,
    get_upload_image_conversation_handler,
)

# Load environment variables
load_dotenv()

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO
)
logger = logging.getLogger(__name__)

def error_handler(update, context):
    """Handle errors."""
    try:
        logger.error(f"Update {update} caused error {context.error}")

        # Notify user only if update is not None and has an effective_message
        if update and hasattr(update, 'effective_message') and update.effective_message:
            update.effective_message.reply_text(
                "⚠️ An error occurred while processing your request.\n"
                "The error has been logged and will be addressed.\n"
                "Please try again or use /cancel to reset the conversation."
            )

    except Exception as e:
        # If error handling itself fails, log it but don't crash
        logger.error(f"Error in error handler: {str(e)}")

def main() -> None:
    """Start the bot."""
    # Get the bot token from environment variable
    token = os.getenv("TELEGRAM_BOT_TOKEN")

    if not token:
        logger.error("No bot token found. Please set the TELEGRAM_BOT_TOKEN environment variable.")
        return

    # Create the Application and pass it your bot's token
    application = Application.builder().token(token).build()

    # Initialize the database
    db.initialize_database()

    # Add conversation handlers
    add_channel_conv = get_add_channel_conversation_handler()
    add_project_conv = get_add_project_conversation_handler()
    custom_time_conv = get_custom_time_conversation_handler()
    add_button_conv = get_add_button_conversation_handler()
    upload_image_conv = get_upload_image_conversation_handler()

    # Register custom time input conversation handler first (high priority)
    logging.info("Registering custom time input conversation handler (high priority)")
    application.add_handler(custom_time_conv)
    logging.info("Custom time input conversation handler registered successfully")

    # Register basic command handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("cancel", cancel))
    application.add_handler(CommandHandler("channels", list_channels))
    application.add_handler(CommandHandler("projects", list_projects))
    application.add_handler(CommandHandler("time", show_server_time))
    application.add_handler(CommandHandler("post", manual_post_command))
    application.add_handler(CommandHandler("status", check_posting_status))
    application.add_handler(CommandHandler("resetlast", reset_last_posted_command))

    # Register conversation handlers
    application.add_handler(add_channel_conv)
    application.add_handler(add_project_conv)
    application.add_handler(add_button_conv)
    application.add_handler(upload_image_conv)

    # Add command button handlers
    application.add_handler(CallbackQueryHandler(handle_command_buttons, pattern=r"^cmd_"))

    # Register project management handlers
    application.add_handler(CallbackQueryHandler(handle_project_settings, pattern=r"^project_settings:"))
    application.add_handler(CallbackQueryHandler(handle_toggle_project, pattern=r"^toggle_project:"))
    application.add_handler(CallbackQueryHandler(handle_delete_project, pattern=r"^delete_project:"))
    application.add_handler(CallbackQueryHandler(handle_confirm_delete, pattern=r"^confirm_delete:"))
    application.add_handler(CallbackQueryHandler(handle_back_to_projects, pattern=r"^back_to_projects$"))

    # Register manual posting handlers
    application.add_handler(CallbackQueryHandler(handle_manual_post, pattern=r"^manual_post:"))
    application.add_handler(CallbackQueryHandler(handle_cancel_manual_post, pattern=r"^cancel_manual_post$"))

    # Register test posting handler
    application.add_handler(CallbackQueryHandler(handle_test_post, pattern=r"^test_post:"))

    # Register content settings handlers
    application.add_handler(CallbackQueryHandler(handle_content_settings, pattern=r"^content_settings:"))
    application.add_handler(CallbackQueryHandler(handle_configure_content, pattern=r"^configure_content:"))
    application.add_handler(CallbackQueryHandler(handle_show_countries, pattern=r"^show_countries:"))
    application.add_handler(CallbackQueryHandler(handle_show_intervals, pattern=r"^show_intervals:"))
    application.add_handler(CallbackQueryHandler(handle_set_country, pattern=r"^set_country:"))
    application.add_handler(CallbackQueryHandler(handle_set_interval, pattern=r"^set_interval:"))
    application.add_handler(CallbackQueryHandler(handle_save_settings, pattern=r"^save_settings:"))
    application.add_handler(CallbackQueryHandler(handle_set_num_coins, pattern=r"^set_num_coins:"))

    # Register scheduling handlers
    application.add_handler(CallbackQueryHandler(handle_set_post_time, pattern=r"^set_post_time:"))
    application.add_handler(CallbackQueryHandler(handle_save_post_time, pattern=r"^save_post_time:"))
    application.add_handler(CallbackQueryHandler(handle_set_frequency, pattern=r"^set_frequency:"))
    application.add_handler(CallbackQueryHandler(handle_save_frequency, pattern=r"^save_frequency:"))
    application.add_handler(CallbackQueryHandler(handle_set_timezone, pattern=r"^set_timezone:"))
    application.add_handler(CallbackQueryHandler(handle_save_timezone, pattern=r"^save_timezone:"))
    application.add_handler(CallbackQueryHandler(handle_view_history, pattern=r"^view_history:"))

    # Register history management handlers
    application.add_handler(CallbackQueryHandler(handle_clear_history, pattern=r"^clear_history:"))
    application.add_handler(CallbackQueryHandler(handle_confirm_clear_history, pattern=r"^confirm_clear_history:"))

    # Register last posted time reset handlers
    application.add_handler(CallbackQueryHandler(handle_reset_last_posted, pattern=r"^reset_last_posted:"))
    application.add_handler(CallbackQueryHandler(handle_confirm_reset_last_posted, pattern=r"^confirm_reset_last_posted:"))

    # Register custom buttons handlers
    application.add_handler(CallbackQueryHandler(handle_custom_buttons, pattern=r"^custom_buttons:"))
    application.add_handler(CallbackQueryHandler(handle_toggle_buttons, pattern=r"^toggle_buttons:"))
    application.add_handler(CallbackQueryHandler(handle_remove_button, pattern=r"^remove_button:"))
    application.add_handler(CallbackQueryHandler(handle_confirm_remove_button, pattern=r"^confirm_remove_button:"))

    # Register image settings handlers
    application.add_handler(CallbackQueryHandler(handle_image_settings, pattern=r"^image_settings:"))
    application.add_handler(CallbackQueryHandler(handle_set_image_mode, pattern=r"^set_image_mode:"))
    application.add_handler(CallbackQueryHandler(handle_reset_image, pattern=r"^reset_image:"))

    # Register error handler
    application.add_error_handler(error_handler)

    # Start content posting threads for active projects
    poster.start_all_active_projects(application.bot)
    logging.info("All active project posting threads started.")

    # Start the Bot
    logging.info("Bot started successfully and is now polling for updates.")

    # Run the bot until you press Ctrl-C
    try:
        logging.info("Bot is running. Press Ctrl+C to stop.")
        application.run_polling()
    except KeyboardInterrupt:
        logging.info("Received keyboard interrupt. Shutting down...")
    finally:
        # Stop all posting threads when the bot is stopped
        logging.info("Stopping all posting threads...")
        poster.stop_all_posting_threads()
        logging.info("Shutdown complete.")

if __name__ == '__main__':
    import asyncio
    asyncio.run(main())
