"""
Project management handlers.
"""
import logging
import uuid
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ConversationHandler

import database as db
import content_poster as poster
from utils.session import get_user_session, set_session_data, get_session_data, clear_session
from utils.keyboards import (
    create_main_menu_keyboard, 
    create_channel_selection_keyboard,
    create_content_type_keyboard,
    create_project_settings_keyboard
)

# Enable logging
logger = logging.getLogger(__name__)

# Conversation states
SELECTING_CHANNEL = 2
SELECTING_CONTENT_TYPE = 3
ENTERING_PROJECT_NAME = 4

async def add_projects(update: Update, context: CallbackContext) -> int:
    """Start the process of adding a project."""
    user_id = update.effective_user.id

    # Get channels for this user
    channels = db.get_channels(user_id)

    # Check if there are any channels
    if not channels:
        # Check if this is a callback query or a direct command
        if update.callback_query:
            query = update.callback_query
            await query.answer()
            await query.edit_message_text(
                "⚠️ You need to add a channel first before creating a project.\n\n"
                "Use /addchannel to add a channel.",
                reply_markup=create_main_menu_keyboard()
            )
        else:
            await update.message.reply_text(
                "⚠️ You need to add a channel first before creating a project.\n\n"
                "Use /addchannel to add a channel.",
                reply_markup=create_main_menu_keyboard()
            )
        return ConversationHandler.END

    # Create a keyboard with all channels
    keyboard = create_channel_selection_keyboard(channels)

    # Check if this is a callback query or a direct command
    if update.callback_query:
        query = update.callback_query
        await query.answer()
        await query.edit_message_text(
            "📢 Select a channel for your new project:",
            reply_markup=keyboard
        )
    else:
        await update.message.reply_text(
            "📢 Select a channel for your new project:",
            reply_markup=keyboard
        )

    return SELECTING_CHANNEL

async def handle_channel_selection(update: Update, context: CallbackContext) -> int:
    """Handle the channel selection for a new project."""
    query = update.callback_query
    await query.answer()

    # Extract channel_id from callback data
    channel_id = query.data.split(':')[1]
    user_id = query.from_user.id

    # Store the selected channel in user session
    set_session_data(user_id, 'selected_channel', channel_id)

    # Show content type selection
    keyboard = create_content_type_keyboard()

    await query.edit_message_text(
        "📊 Select the content type for your project:",
        reply_markup=keyboard
    )

    return SELECTING_CONTENT_TYPE

async def handle_content_type_selection(update: Update, context: CallbackContext) -> int:
    """Handle the content type selection for a new project."""
    query = update.callback_query
    await query.answer()

    # Extract content_type from callback data
    content_type = query.data.split(':')[1]
    user_id = query.from_user.id

    # Store the selected content type in user session
    set_session_data(user_id, 'content_type', content_type)

    # Ask for project name
    await query.edit_message_text(
        "📝 Please enter a name for your project:"
    )

    return ENTERING_PROJECT_NAME

async def handle_project_name(update: Update, context: CallbackContext) -> int:
    """Handle the project name input and create the project."""
    user_id = update.effective_user.id
    project_name = update.message.text.strip()

    if not project_name:
        await update.message.reply_text("Please enter a valid project name.")
        return ENTERING_PROJECT_NAME

    # Get user session data
    channel_id = get_session_data(user_id, 'selected_channel')
    content_type = get_session_data(user_id, 'content_type')

    if not channel_id or not content_type:
        await update.message.reply_text(
            "Something went wrong. Please start over with /addprojects."
        )
        return ConversationHandler.END

    # Get channel info for this user
    channels = db.get_channels(user_id)
    channel_info = channels.get(channel_id, {})

    # Create project
    project_id = str(uuid.uuid4())
    channel_config = {
        "channel_id": channel_id,
        "channel_name": channel_info.get('name', 'Unknown'),
        "content_type": content_type
    }

    success = db.add_project(user_id, project_id, project_name, [channel_config])

    if success:
        # Initialize content settings based on content type
        if content_type == "daily_news_summary":
            content_settings = {
                "country": "India",
                "post_interval_hours": 24,
                "post_time_hour": 8,
                "post_time_minute": 0,
                "timezone_country": "India",
                "last_posted": None
            }
            db.update_project_content_settings(user_id, project_id, "daily_news_summary", content_settings)
        elif content_type == "crypto_prices":
            content_settings = {
                "num_coins": 4,
                "post_interval_hours": 24,
                "post_time_hour": 9,
                "post_time_minute": 0,
                "timezone_country": "India",
                "last_posted": None
            }
            db.update_project_content_settings(user_id, project_id, "crypto_prices", content_settings)
        elif content_type == "health_fitness":
            content_settings = {
                "post_interval_hours": 24,
                "post_time_hour": 7,
                "post_time_minute": 0,
                "timezone_country": "India",
                "last_posted": None
            }
            db.update_project_content_settings(user_id, project_id, "health_fitness", content_settings)
        elif content_type == "cricket_news":
            content_settings = {
                "post_interval_hours": 24,
                "post_time_hour": 10,
                "post_time_minute": 0,
                "timezone_country": "India",
                "last_posted": None
            }
            db.update_project_content_settings(user_id, project_id, "cricket_news", content_settings)

        # Initialize image settings
        image_settings = {
            "mode": "default",
            "custom_image_path": None
        }
        db.update_project_image_settings(user_id, project_id, image_settings)

        # Clear session data
        clear_session(user_id)

        # Create a keyboard for project settings
        keyboard = [
            [InlineKeyboardButton("⚙️ Configure Project", callback_data=f"project_settings:{project_id}")],
            [InlineKeyboardButton("📤 Test Post Now", callback_data=f"test_post:{project_id}")],
            [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
        ]

        await update.message.reply_text(
            f"✅ Project '{project_name}' created successfully!\n\n"
            f"You can now configure the project settings or test it by posting content immediately.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        await update.message.reply_text(
            "❌ Failed to create project. Please try again."
        )

    return ConversationHandler.END

async def list_projects(update: Update, context: CallbackContext) -> None:
    """List all projects for the current user."""
    user_id = update.effective_user.id

    # Get projects for this user
    projects = db.get_projects(user_id)

    # Check if there are any projects
    if not projects:
        # Check if this is a callback query or a direct command
        if update.callback_query:
            query = update.callback_query
            await query.answer()
            await query.edit_message_text(
                "📊 You haven't created any projects yet.\n\n"
                "Use /addprojects to create a project.",
                reply_markup=create_main_menu_keyboard()
            )
        else:
            await update.message.reply_text(
                "📊 You haven't created any projects yet.\n\n"
                "Use /addprojects to create a project.",
                reply_markup=create_main_menu_keyboard()
            )
        return

    # Create a keyboard with all projects
    keyboard = []
    for project_id, project_info in projects.items():
        project_name = project_info.get('name', 'Unknown')
        is_active = project_info.get('active', False)
        status_emoji = "🟢" if is_active else "🔴"

        keyboard.append([
            InlineKeyboardButton(
                f"{status_emoji} {project_name}",
                callback_data=f"project_settings:{project_id}"
            )
        ])

    # Add a button to create a new project
    keyboard.append([
        InlineKeyboardButton("➕ Add New Project", callback_data="cmd_addprojects")
    ])

    # Add a button to go back to the main menu
    keyboard.append([
        InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")
    ])

    # Check if this is a callback query or a direct command
    if update.callback_query:
        query = update.callback_query
        await query.answer()
        await query.edit_message_text(
            "📊 *Your Projects*\n\n"
            "Select a project to manage its settings:",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        await update.message.reply_text(
            "📊 *Your Projects*\n\n"
            "Select a project to manage its settings:",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

def handle_project_settings(update: Update, context: CallbackContext) -> None:
    """Handle project settings."""
    query = update.callback_query
    query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
        return
    
    project_name = project_info.get('name', 'Unknown')
    is_active = project_info.get('active', False)
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    # Get content settings
    content_settings = project_info.get('content_settings', {})
    
    # Create settings message
    message = f"⚙️ *Project Settings: {project_name}*\n\n"
    
    # Add content type
    if content_type == "daily_news_summary":
        message += "📰 *Content Type:* Daily News Summary\n"
    elif content_type == "crypto_prices":
        message += "💰 *Content Type:* Crypto Prices\n"
    elif content_type == "health_fitness":
        message += "💪 *Content Type:* Health & Fitness\n"
    else:
        message += f"📄 *Content Type:* {content_type}\n"
    
    # Add status
    message += f"📊 *Status:* {'Active' if is_active else 'Inactive'}\n\n"
    
    # Add channel info
    if project_info.get("channels"):
        channel_config = project_info["channels"][0]
        channel_name = channel_config.get("channel_name", "Unknown")
        message += f"📢 *Channel:* {channel_name}\n\n"
    
    # Add posting settings
    if content_type and content_type in content_settings:
        settings = content_settings[content_type]
        post_time_hour = settings.get('post_time_hour', 8)
        post_time_minute = settings.get('post_time_minute', 0)
        timezone_country = settings.get('timezone_country', 'India')
        last_posted = settings.get('last_posted', 'Never')
        
        message += f"⏱️ *Posting Time:* {post_time_hour:02d}:{post_time_minute:02d}\n"
        message += f"🌐 *Timezone:* {timezone_country}\n"
        message += f"🔄 *Last Posted:* {last_posted}\n"
    
    # Create settings keyboard
    keyboard = create_project_settings_keyboard(project_id, is_active)
    
    query.edit_message_text(
        message,
        parse_mode="Markdown",
        reply_markup=keyboard
    )

def handle_toggle_project(update: Update, context: CallbackContext) -> None:
    """Handle toggling a project's active status."""
    query = update.callback_query
    query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
        return
    
    # Toggle active status
    is_active = project_info.get('active', False)
    new_status = not is_active
    
    # Update project status
    db.update_project_status(project_id, new_status)
    
    # If activating, start the posting thread
    if new_status:
        poster.start_posting_thread(context.bot, project_id)
    else:
        poster.stop_posting_thread(project_id)
    
    # Show updated settings
    handle_project_settings(update, context)

def handle_delete_project(update: Update, context: CallbackContext) -> None:
    """Handle deleting a project."""
    query = update.callback_query
    query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
        return
    
    project_name = project_info.get('name', 'Unknown')
    
    # Create confirmation keyboard
    keyboard = [
        [
            InlineKeyboardButton("✅ Yes, Delete", callback_data=f"confirm_delete:{project_id}"),
            InlineKeyboardButton("❌ No, Cancel", callback_data=f"project_settings:{project_id}")
        ]
    ]
    
    query.edit_message_text(
        f"⚠️ *Delete Project*\n\n"
        f"Are you sure you want to delete the project '{project_name}'?\n\n"
        f"This action cannot be undone.",
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_confirm_delete(update: Update, context: CallbackContext) -> None:
    """Handle confirming project deletion."""
    query = update.callback_query
    query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
        return
    
    project_name = project_info.get('name', 'Unknown')
    
    # Stop the posting thread if it's running
    poster.stop_posting_thread(project_id)
    
    # Delete the project
    success = db.delete_project(project_id)
    
    if success:
        query.edit_message_text(
            f"✅ Project '{project_name}' has been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
    else:
        query.edit_message_text(
            f"❌ Failed to delete project '{project_name}'.",
            reply_markup=create_main_menu_keyboard()
        )

def handle_back_to_projects(update: Update, context: CallbackContext) -> None:
    """Handle going back to the projects list."""
    list_projects(update, context)

def handle_test_post(update: Update, context: CallbackContext) -> None:
    """Handle test posting."""
    query = update.callback_query
    query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
        return
    
    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")
    
    # Show loading message based on content type
    if content_type == "daily_news_summary":
        query.edit_message_text(
            "🔄 Generating and posting Daily News Summary...\n"
            "This may take a moment."
        )
    elif content_type == "crypto_prices":
        query.edit_message_text(
            "🔄 Generating and posting Crypto Prices...\n"
            "This may take a moment."
        )
    elif content_type == "health_fitness":
        query.edit_message_text(
            "🔄 Generating and posting Health & Fitness content...\n"
            "This may take a moment."
        )
    else:
        query.edit_message_text(
            "🔄 Generating and posting content...\n"
            "This may take a moment."
        )
    
    # Attempt to post content with bypass_time_check=True
    success, message = poster.post_content(context.bot, project_id, test_mode=True, bypass_time_check=True)
    
    # Create a keyboard to go back to project settings
    keyboard = [
        [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
    ]
    
    if success:
        query.edit_message_text(
            f"✅ Test post successful!\n\n{message}",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        query.edit_message_text(
            f"❌ Test post failed: {message}",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

def manual_post_command(update: Update, context: CallbackContext) -> None:
    """Handle the /post command for manual posting."""
    # Check if a project ID was provided
    if not context.args or len(context.args) < 1:
        # No project ID provided, show a list of projects
        projects = db.get_projects()
        
        if not projects:
            update.message.reply_text("No projects found.")
            return
        
        # Create a keyboard with all projects
        keyboard = []
        for project_id, project_info in projects.items():
            project_name = project_info.get('name', 'Unknown')
            keyboard.append([
                InlineKeyboardButton(
                    f"📤 Post {project_name}",
                    callback_data=f"manual_post:{project_id}"
                )
            ])
        
        update.message.reply_text(
            "Select a project to post content manually:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        return
    
    # Project ID was provided
    project_id = context.args[0]
    
    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id)
    
    if not project_info:
        update.message.reply_text(f"Project with ID {project_id} not found.")
        return
    
    # Show a processing message
    message = update.message.reply_text("Processing your request... Please wait.")
    
    # Trigger the post
    success, result = poster.manual_post(context.bot, project_id)
    
    # Show the result
    if success:
        message.edit_text(f"✅ Successfully posted content: {result}")
    else:
        message.edit_text(f"❌ Failed to post content: {result}")

def handle_manual_post(update: Update, context: CallbackContext) -> None:
    """Handle manual post button click."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Show a processing message
    query.edit_message_text("Processing your request... Please wait.")

    # Trigger the post
    success, message = poster.manual_post(context.bot, project_id)

    # Create a keyboard to go back to project settings
    keyboard = [
        [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
    ]

    # Show the result
    if success:
        query.edit_message_text(
            f"✅ Successfully posted content: {message}",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        query.edit_message_text(
            f"❌ Failed to post content: {message}",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

def handle_cancel_manual_post(update: Update, context: CallbackContext) -> None:
    """Handle cancellation of manual post."""
    query = update.callback_query
    query.answer()

    query.edit_message_text(
        "❌ Manual post cancelled.",
        reply_markup=create_main_menu_keyboard()
    )

def handle_view_history(update: Update, context: CallbackContext) -> None:
    """Handle viewing posting history."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
        return

    project_name = project_info.get('name', 'Unknown')

    # Get posting history
    history = db.get_posting_history(project_id)

    if not history:
        message = f"📊 *Posting History: {project_name}*\n\n"
        message += "No posting history found."
    else:
        message = f"📊 *Posting History: {project_name}*\n\n"

        # Show last 10 posts
        recent_history = history[-10:] if len(history) > 10 else history

        for i, entry in enumerate(reversed(recent_history), 1):
            timestamp = entry.get('timestamp', 'Unknown')
            content_type = entry.get('content_type', 'Unknown')
            status = entry.get('status', 'Unknown')

            status_emoji = "✅" if status == "success" else "❌"
            message += f"{i}. {status_emoji} {content_type}\n"
            message += f"   {timestamp}\n\n"

        if len(history) > 10:
            message += f"... and {len(history) - 10} more entries"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("🗑️ Clear History", callback_data=f"clear_history:{project_id}")],
        [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
    ]

    query.edit_message_text(
        message,
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_clear_history(update: Update, context: CallbackContext) -> None:
    """Handle clearing posting history."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
        return

    project_name = project_info.get('name', 'Unknown')

    # Create confirmation keyboard
    keyboard = [
        [
            InlineKeyboardButton("✅ Yes, Clear", callback_data=f"confirm_clear_history:{project_id}"),
            InlineKeyboardButton("❌ No, Cancel", callback_data=f"view_history:{project_id}")
        ]
    ]

    query.edit_message_text(
        f"⚠️ *Clear Posting History*\n\n"
        f"Are you sure you want to clear all posting history for '{project_name}'?\n\n"
        f"This action cannot be undone.",
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

def handle_confirm_clear_history(update: Update, context: CallbackContext) -> None:
    """Handle confirming history clearing."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
        return

    project_name = project_info.get('name', 'Unknown')

    # Clear the history
    success = db.clear_posting_history(project_id)

    # Create keyboard to go back to project settings
    keyboard = [
        [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
    ]

    if success:
        query.edit_message_text(
            f"✅ Posting history for '{project_name}' has been cleared.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        query.edit_message_text(
            f"❌ Failed to clear posting history for '{project_name}'.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

def handle_confirm_reset_last_posted(update: Update, context: CallbackContext) -> None:
    """Handle confirming last posted time reset."""
    query = update.callback_query
    query.answer()

    # Extract project_id from callback data
    project_id = query.data.split(':')[1]

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=create_main_menu_keyboard()
        )
        return

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    if not content_type:
        query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return

    # Reset last posted time
    success = db.reset_last_posted_time(project_id, content_type)

    # Create keyboard to go back to project settings
    keyboard = [
        [InlineKeyboardButton("⬅️ Back to Project Settings", callback_data=f"project_settings:{project_id}")]
    ]

    if success:
        query.edit_message_text(
            f"✅ Last posted time for project '{project_info.get('name')}' has been reset.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        query.edit_message_text(
            "❌ Failed to reset last posted time.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
