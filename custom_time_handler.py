def handle_custom_time_input(update, context):
    """Handle custom time input."""
    user_id = update.effective_user.id
    time_text = update.message.text.strip()

    # Parse time input
    hour = 0
    minute = 0
    
    try:
        # Check if input contains a colon (HH:MM format)
        if ':' in time_text:
            parts = time_text.split(':')
            if len(parts) != 2:
                raise ValueError("Invalid time format")
                
            hour = int(parts[0])
            minute = int(parts[1])
            
            # Validate hour and minute
            if hour < 0 or hour > 23 or minute < 0 or minute > 59:
                raise ValueError("Invalid time range")
        else:
            # Just hour format
            hour = int(time_text)
            if hour < 0 or hour > 23:
                raise ValueError("Invalid hour range")
    except ValueError as e:
        update.message.reply_text(
            "⚠️ Invalid time format. Please enter time as HH:MM (e.g., 21:30) or just the hour (e.g., 21)."
        )
        return ENTERING_CUSTOM_TIME

    # Get session data
    if user_id not in user_sessions:
        update.message.reply_text("Session expired. Please start over.")
        return ConversationHandler.END

    project_id = user_sessions[user_id].get('project_id')

    if not project_id:
        update.message.reply_text("Session data missing. Please start over.")
        return ConversationHandler.END

    # Get project info
    projects = db.get_projects()
    project_info = projects.get(project_id, {})

    if not project_info:
        update.message.reply_text("Project not found. It may have been deleted.")
        return ConversationHandler.END

    # Get content type from the first channel
    content_type = None
    if project_info.get("channels"):
        content_type = project_info["channels"][0].get("content_type")

    # Update posting time in the appropriate content settings
    content_settings = project_info.get('content_settings', {})
    time_str = f"{hour:02d}:{minute:02d}" if minute > 0 else f"{hour}:00"
    
    if content_type == "daily_news_summary" and "daily_news_summary" in content_settings:
        settings = content_settings["daily_news_summary"]
        settings["post_time_hour"] = hour
        settings["post_time_minute"] = minute
        db.update_project_content_settings(project_id, "daily_news_summary", settings)
    elif content_type == "crypto_prices" and "crypto_prices" in content_settings:
        settings = content_settings["crypto_prices"]
        settings["post_time_hour"] = hour
        settings["post_time_minute"] = minute
        db.update_project_content_settings(project_id, "crypto_prices", settings)

    # Send confirmation
    keyboard = [
        [InlineKeyboardButton(
            "⬅️ Back to Project Settings",
            callback_data=f"project_settings:{project_id}"
        )]
    ]

    update.message.reply_text(
        f"✅ Posting time updated to {time_str}",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

    # Clear session data
    if user_id in user_sessions:
        del user_sessions[user_id]

    return ConversationHandler.END
