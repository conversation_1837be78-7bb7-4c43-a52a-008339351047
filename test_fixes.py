#!/usr/bin/env python3
"""
Test script to verify custom image and custom buttons fixes.
"""
import os
import sys
import asyncio
import logging
from unittest.mock import Mock, AsyncMock

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import database as db
import content_poster

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_custom_image_and_buttons():
    """Test custom image and buttons functionality."""
    print("🧪 Testing Custom Image and Buttons Fixes")
    print("=" * 50)
    
    # Test project ID
    project_id = 'c02e3ab9-e196-4d5c-b350-fca40d4ccea9'
    
    # Get project info
    projects = db.get_all_projects_for_posting()
    project_info = projects.get(project_id, {})
    
    if not project_info:
        print(f"❌ Project {project_id} not found!")
        return
    
    print(f"✅ Project found: {project_info.get('name', 'Unknown')}")
    
    # Test 1: Check image settings
    print("\n1. 🖼️ TESTING IMAGE SETTINGS:")
    image_settings = project_info.get('image_settings', {})
    print(f"   Image settings: {image_settings}")
    
    mode = image_settings.get('mode', 'default')
    print(f"   Image mode: {mode}")
    
    if mode == 'custom':
        custom_image_path = image_settings.get('custom_image_path')
        print(f"   Custom image path: {custom_image_path}")
        
        if custom_image_path and os.path.exists(custom_image_path):
            print(f"   ✅ Custom image file exists: {custom_image_path}")
            file_size = os.path.getsize(custom_image_path)
            print(f"   File size: {file_size} bytes")
        else:
            print(f"   ❌ Custom image file not found: {custom_image_path}")
    else:
        print(f"   Using default image mode: {mode}")
    
    # Test 2: Check button settings
    print("\n2. 🔘 TESTING BUTTON SETTINGS:")
    button_settings = project_info.get('button_settings', {})
    print(f"   Button settings: {button_settings}")
    
    buttons_enabled = button_settings.get('enabled', False)
    buttons = button_settings.get('buttons', [])
    print(f"   Buttons enabled: {buttons_enabled}")
    print(f"   Number of buttons: {len(buttons)}")
    
    if buttons:
        for i, button in enumerate(buttons):
            print(f"   Button {i+1}: '{button.get('text')}' -> {button.get('url')}")
    
    # Test 3: Simulate the posting logic
    print("\n3. 🚀 TESTING POSTING LOGIC:")
    
    # Mock bot and channel
    mock_bot = Mock()
    mock_bot.id = 123456789
    mock_bot.get_chat_member = AsyncMock()
    mock_bot.send_photo = AsyncMock()
    
    # Mock chat member with admin permissions
    mock_member = Mock()
    mock_member.status = 'administrator'
    mock_member.can_post_messages = True
    mock_bot.get_chat_member.return_value = mock_member
    
    # Test channel ID
    channel_id = -1002861074047
    
    # Test message
    test_message = "🏏 *TEST CRICKET NEWS*\n\n_This is a test post to verify custom image and buttons functionality._"
    
    print(f"   Testing send_message_to_channel with project_id: {project_id}")
    
    try:
        # Call the actual send_message_to_channel function
        success, error_msg = await content_poster.send_message_to_channel(
            mock_bot, channel_id, test_message, project_id
        )
        
        print(f"   Result: success={success}, error='{error_msg}'")
        
        # Check if send_photo was called (indicates image was processed)
        if mock_bot.send_photo.called:
            print("   ✅ send_photo was called (image processing worked)")
            call_args = mock_bot.send_photo.call_args
            if call_args:
                kwargs = call_args.kwargs
                print(f"   Chat ID: {kwargs.get('chat_id')}")
                print(f"   Caption length: {len(kwargs.get('caption', ''))}")
                
                # Check if reply_markup was included (indicates buttons were added)
                reply_markup = kwargs.get('reply_markup')
                if reply_markup:
                    print("   ✅ reply_markup included (custom buttons were added)")
                    print(f"   Number of button rows: {len(reply_markup.inline_keyboard)}")
                else:
                    print("   ❌ No reply_markup (custom buttons were NOT added)")
        else:
            print("   ❌ send_photo was NOT called")
            
    except Exception as e:
        print(f"   ❌ Error during testing: {e}")
    
    print("\n" + "=" * 50)
    print("🧪 Test completed!")

async def main():
    """Main test function."""
    await test_custom_image_and_buttons()

if __name__ == "__main__":
    asyncio.run(main())
