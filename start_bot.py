#!/usr/bin/env python3
"""
Production startup script for Telegram Auto-Posting Bot.
This script provides enhanced error handling, logging, and restart capabilities.
"""

import os
import sys
import time
import logging
import signal
import subprocess
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot_startup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class BotManager:
    def __init__(self):
        self.bot_process = None
        self.should_restart = True
        self.restart_count = 0
        self.max_restarts = 10
        
    def check_environment(self):
        """Check if all required environment variables are set."""
        required_vars = [
            'TELEGRAM_BOT_TOKEN',
            'GEMINI_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
            logger.error("Please check your .env file or environment configuration.")
            return False
        
        logger.info("✅ All required environment variables are set")
        return True
    
    def check_dependencies(self):
        """Check if all required Python packages are installed."""
        try:
            import telegram
            import google.generativeai
            import requests
            import dotenv
            logger.info("✅ All required dependencies are available")
            return True
        except ImportError as e:
            logger.error(f"Missing required dependency: {e}")
            logger.error("Please run: pip install -r requirements.txt")
            return False
    
    def create_data_directories(self):
        """Create necessary data directories."""
        directories = ['data', 'data/images']
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info("✅ Data directories created/verified")
    
    def start_bot(self):
        """Start the bot process."""
        try:
            logger.info("🚀 Starting Telegram Auto-Posting Bot...")
            self.bot_process = subprocess.Popen(
                [sys.executable, 'bot.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Log bot output in real-time
            for line in iter(self.bot_process.stdout.readline, ''):
                if line:
                    print(line.strip())
            
            return_code = self.bot_process.wait()
            logger.info(f"Bot process exited with code: {return_code}")
            return return_code
            
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            return 1
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, shutting down...")
        self.should_restart = False
        if self.bot_process:
            self.bot_process.terminate()
            try:
                self.bot_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning("Bot didn't shut down gracefully, forcing termination")
                self.bot_process.kill()
        sys.exit(0)
    
    def run(self):
        """Main run loop with restart capability."""
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Pre-flight checks
        if not self.check_environment():
            sys.exit(1)
        
        if not self.check_dependencies():
            sys.exit(1)
        
        self.create_data_directories()
        
        # Main loop
        while self.should_restart and self.restart_count < self.max_restarts:
            if self.restart_count > 0:
                logger.info(f"Restarting bot (attempt {self.restart_count + 1}/{self.max_restarts})")
                time.sleep(5)  # Wait before restart
            
            return_code = self.start_bot()
            
            if return_code == 0:
                logger.info("Bot exited normally")
                break
            else:
                self.restart_count += 1
                logger.warning(f"Bot exited with error code {return_code}")
                
                if self.restart_count >= self.max_restarts:
                    logger.error("Maximum restart attempts reached. Stopping.")
                    break
        
        logger.info("Bot manager shutting down")

def main():
    """Main entry point."""
    print("🤖 Telegram Auto-Posting Bot - Production Manager")
    print("=" * 50)
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        logger.info("✅ Environment variables loaded")
    except ImportError:
        logger.warning("python-dotenv not available, using system environment")
    
    # Start bot manager
    manager = BotManager()
    manager.run()

if __name__ == "__main__":
    main()
